# Copilot Instructions for Vue 3 ERP Frontend

## Project Architecture Overview

This is a Vue 3 + TypeScript enterprise ERP frontend with a sophisticated **plugin-based data grid system** and **module-driven API architecture**. The system is designed for scalability with 11+ business modules (bas, fin, hr, mes, etc.).

### Core Technology Stack

- **Framework**: Vue 3 Composition API + TypeScript + Vite
- **UI**: Tailwind CSS + Shadcn vue/ui components
- **State**: Pinia stores
- **Data Fetching**: Alova.js with module-based APIs
- **Tables**: VxeTable with custom plugin system
- **Package Manager**: pnpm

## Key Architecture Patterns

### 1. Data Grid Plugin System

**Location**: `src/components/data-grid/`

The heart of the application is a plugin-based data grid system that auto-generates tables from API metadata:

```typescript
// Core pattern: useDataGrid composable
const dataGridInstance = useDataGrid('demo/demo', {
  enableSelection: 'checkbox',
  columns: [], // Auto-generated or manually configured
})

// Column helpers with TypeScript intellisense
const columnHelper = dataGridInstance.getColumnHelper()
const columns = [
  columnHelper.composite('customer_info', '客户信息', {
    /* config */
  }),
  columnHelper.status('status', '状态', { variant: 'badge' }),
  columnHelper.relationList('demo_items', '订单项', { maxDisplay: 2 }),
]
```

**Critical files**:

- `useDataGrid.ts` - Main composable with caching and performance optimization
- `plugins/manager.ts` - Plugin registration and column helper factory
- `plugins/renderers/` - Modular renderer components (status, boolean, actions, etc.)

### 2. Module-Based API System

**Location**: `src/api/`

APIs are organized by business modules with auto-generated TypeScript interfaces:

```typescript
// Pattern: module/model structure
const api = await ApiService.getApi('demo/demo') // Module: demo, Model: demo
const api2 = await ApiService.getApi('sal/customer') // Module: sal, Model: customer

// Each API provides standard CRUD + metadata
interface ModelApi {
  getMetadata: () => Promise<ResponseModel<CMetadata>>
  getList: (params?: QueryParams) => Promise<ResponseListModel<T>>
  create?: (data: C) => Promise<T>
  // ... other CRUD methods
}
```

**Auto-generation**: Run `pnpm generate:api` to extract TypeScript interfaces from backend schemas.

### 3. Component Plugin Architecture

**Location**: `src/components/common/Table/plugins/`

Two plugin systems coexist:

- **Legacy Table plugins**: Complex feature-rich system in `common/Table/plugins/`
- **New DataGrid plugins**: Simplified performance-focused system in `data-grid/plugins/`

Always use the **new DataGrid system** for new development.

## Essential Development Workflows

### Setup & Development

```bash
pnpm install           # Install dependencies
pnpm dev              # Start dev server (port 4111)
pnpm generate:api     # Generate API types from backend
pnpm generate:table   # Generate table components
```

### Code Quality

```bash
pnpm type:check       # TypeScript validation
pnpm lint            # Run all linters (ESLint + Prettier + Stylelint)
pnpm lint:eslint     # ESLint with auto-fix
```

### Testing & Debugging

- Use Vue DevTools for component inspection
- Performance monitoring built into data grid system
- Memory optimization via automatic cache cleanup

## Project-Specific Conventions

### File Naming

- **Vue components**: UpperCamelCase for common components (`UserProfile.vue`)
- **Page components**: kebab-case (`user-profile.vue`)
- **TypeScript files**: kebab-case (`auth-service.ts`)
- **Directories**: kebab-case (`user-management/`)

### Font & Styling Standards

Use predefined typography classes instead of custom styles:

```vue
<!-- Chinese text -->
<h1 class="text-title">标题 (14px bold #203251)</h1>
<p class="text-content">内容 (12px normal #333333)</p>
<span class="text-action">操作 (12px normal #3399FF)</span>

<!-- English text -->
<h1 class="text-title-en">Title</h1>
<p class="text-content-en">Content</p>
<span class="text-action-en">Action</span>
```

### Git Commit Prefixes

- `feat:` - New features
- `fix:` - Bug fixes
- `refactor:` - Code refactoring
- `style:` - UI/styling changes
- `perf:` - Performance improvements

## Common Integration Patterns

### Creating Data Grids

```vue
<script setup lang="ts">
import { useDataGrid } from '@/components/data-grid/composables/useDataGrid'
import DataGrid from '@/components/data-grid/core/DataGrid.vue'

// 1. Create data grid instance
const dataGridInstance = useDataGrid('module/model', {
  enableSelection: 'checkbox',
  toolbarOptions: {
    title: { main: '数据管理', sub: '智能表格' }
  }
})

// 2. Configure columns with helpers
onMounted(() => {
  const columnHelper = dataGridInstance.getColumnHelper()
  dataGridInstance.gridOptions.value.columns = [
    columnHelper.composite('user_info', '用户信息'),
    columnHelper.status('status', '状态'),
    columnHelper.actions('操作', { actions: [...] })
  ]
})
</script>

<template>
  <DataGrid :data-grid-instance="dataGridInstance" />
</template>
```

### Adding Custom Renderers

```typescript
// 1. Create renderer component in plugins/renderers/
// 2. Register in plugins/index.ts
const customRenderer: PluginRenderer = {
  name: 'CustomRenderer',
  component: CustomComponent,
  defaultWidth: 120,
}

// 3. Add to manager
pluginManager.registerRenderer(customRenderer)

// 4. Extend column helper
columnHelper.custom = (field, title, config) =>
  columnHelper.createColumn(field, title, 'CustomRenderer', config)
```

### Performance Optimization

- Use `shallowRef` for large data sets
- Leverage built-in column caching: `globalColumnCache.getOrCreate()`
- Monitor with `lightweightMonitor` for render performance
- Clear caches with `clearCacheForModule()` on unmount

## Memory Management

The system includes automatic cleanup:

```typescript
onUnmounted(() => {
  // Clear module-specific caches
  clearCacheForModule('demo/demo')

  // Clear performance monitoring
  performanceMonitor.stopMonitoring('demo/demo')

  // Clear column cache if needed
  globalColumnCache.delete(CACHE_KEY)
})
```

## Debugging Tips

### Plugin System

```typescript
// Check available column helpers
const debugInfo = pluginManager.getDebugInfo?.() || {}
console.log('Available helpers:', debugInfo.columnHelpers)

// Verify plugin registration
console.log('Registered renderers:', pluginManager.getRegisteredRenderers())
```

### Data Grid Issues

```typescript
// Check grid instance state
console.log('Grid options:', dataGridInstance.gridOptions.value)
console.log('Current data:', dataGridInstance.gridOptions.value.data)
console.log('Loading state:', dataGridInstance.loading.value)
```

When working with this codebase, prioritize the new data grid system over legacy table components, leverage the plugin architecture for extensibility, and always use the provided typography and naming conventions for consistency.
