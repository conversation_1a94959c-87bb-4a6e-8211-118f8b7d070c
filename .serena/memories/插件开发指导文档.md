# Data-Grid 插件开发指导文档

## 📋 概述

Data-Grid 插件系统是一个轻量级、可扩展的组件渲染系统，支持在表格列中嵌入自定义组件。系统基于 Vue 3 + TypeScript，提供了丰富的内置渲染器和简单的扩展机制。

## 🏗️ 架构概览

### 核心组件

```
plugins/
├── manager.ts              # 插件管理器核心
├── types/index.ts          # 基础类型定义
├── utils/rendererUtils.ts  # 渲染器工具函数
├── index.ts               # 插件系统入口
└── renderers/             # 内置渲染器
    ├── status/           # 状态渲染器
    ├── boolean/          # 布尔值渲染器
    ├── link/             # 链接渲染器
    ├── actions/          # 操作按钮渲染器
    └── composite/        # 复合渲染器
```

### 工作原理

1. **插件注册**：通过 PluginManager 注册组件和渲染器
2. **列配置**：使用 ColumnHelper 创建包含插件的列配置
3. **动态渲染**：DataGrid 解析组件标识符并动态渲染组件
4. **配置存储**：通过全局配置存储管理复杂配置对象

## 🔧 开发插件

### 1. 基础插件结构

每个插件由以下几部分组成：

```typescript
// types.ts - 类型定义
export interface MyRendererConfig extends BaseRendererConfig {
  // 自定义配置属性
  customOption?: string
  variant?: 'type1' | 'type2'
}

export interface MyRendererProps extends BaseRendererProps {
  config?: MyRendererConfig
}
```

```typescript
// useRenderer.ts - 业务逻辑
export const useMyRenderer = (props: MyRendererProps) => {
  const value = computed(() => {
    return props.field ? props.row?.[props.field] : props.value
  })

  const variant = computed(() => props.config?.variant || 'type1')

  // 返回组件需要的响应式数据
  return {
    value,
    variant,
    // 其他计算属性
  }
}
```

```vue
<!-- renderer.vue - 组件模板 -->
<template>
  <div v-if="variant === 'type1'">
    <!-- 类型1 渲染 -->
    {{ value }}
  </div>
  <div v-else>
    <!-- 类型2 渲染 -->
    {{ value }}
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'MyRenderer',
})

import type { MyRendererProps } from './types'
import { useMyRenderer } from './useRenderer'

const props = defineProps<MyRendererProps>()
const { value, variant } = useMyRenderer(props)
</script>
```

### 2. 注册插件

```typescript
// index.ts - 插件导出
export { default as MyRenderer } from './renderer.vue'
export * from './types'
export * from './useRenderer'
```

```typescript
// 在主插件文件中注册
import { MyRenderer } from './renderers/my-renderer'

const coreRenderers: PluginRenderer[] = [
  // 其他渲染器...
  {
    name: 'MyRenderer',
    component: MyRenderer,
    defaultWidth: 150, // 可选的默认宽度
    defaultConfig: {   // 可选的默认配置
      variant: 'type1'
    }
  },
]
```

### 3. 扩展 ColumnHelper

```typescript
// 在 manager.ts 的 PluginHelper 类中添加方法
export class PluginHelper {
  // 现有方法...

  /**
   * 创建自定义渲染器列
   */
  myRenderer(
    field: string,
    title: string,
    config?: MyRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'MyRenderer', {
      variant: 'type1',
      ...config,
    })
  }
}
```

## 🎯 内置渲染器使用指南

### StatusRenderer - 状态渲染器

支持 4 种变体：badge、dot、text、progress

```typescript
// 使用示例
const columnHelper = manager.getColumnHelper()

// 基础状态列
const statusColumn = columnHelper.status('status', '状态', {
  variant: 'badge',
  statusMap: {
    active: { text: '活跃', type: 'success', icon: 'mdi:check' },
    inactive: { text: '未激活', type: 'default' }
  }
})

// 自动从元数据生成状态映射
const autoStatusColumn = columnHelper.status('status', '状态', {
  autoFromMetadata: true  // 自动从 enumInfo 生成
})

// 进度变体
const progressColumn = columnHelper.status('progress', '进度', {
  variant: 'progress',
  statusMap: {
    'in_progress': { text: '进行中', type: 'primary', progress: 60 }
  }
})
```

### BooleanRenderer - 布尔值渲染器

用于显示是/否、开/关等二元状态

```typescript
// 使用示例
const booleanColumn = columnHelper.boolean('isActive', '是否激活', {
  variant: 'badge',  // 'badge' | 'switch' | 'icon'
  trueText: '已激活',
  falseText: '未激活',
  trueIcon: 'mdi:check',
  falseIcon: 'mdi:close'
})
```

### LinkRenderer - 链接渲染器

支持邮件、电话、URL 等链接类型

```typescript
// URL 链接
const linkColumn = columnHelper.link('website', '网站', {
  target: '_blank',
  showExternal: true,
  truncate: true
})

// 邮件链接
const mailColumn = columnHelper.mail('email', '邮箱', {
  showTypeIcon: true
})

// 电话链接
const phoneColumn = columnHelper.phone('phone', '电话')
```

### ActionsRenderer - 操作按钮渲染器

用于在列中显示操作按钮

```typescript
const actionsColumn = columnHelper.actions('操作', {
  layout: 'horizontal',  // 'horizontal' | 'dropdown'
  actions: [
    {
      label: '编辑',
      icon: 'mdi:edit',
      variant: 'primary',
      onClick: (row) => { /* 处理编辑 */ }
    },
    {
      label: '删除',
      icon: 'mdi:delete',
      variant: 'destructive',
      onClick: (row) => { /* 处理删除 */ }
    }
  ]
})
```

### CompositeRenderer - 复合渲染器

支持图标、主内容、子内容和操作按钮的组合显示

```typescript
const compositeColumn = columnHelper.composite('user', '用户信息', {
  icon: {
    type: 'avatar',
    avatarField: 'avatar',
    nameField: 'name'
  },
  main: {
    field: 'name',
    linkField: 'profileUrl'
  },
  subs: {
    items: [
      { field: 'email', icon: 'mdi:email' },
      { field: 'department', icon: 'mdi:office-building' }
    ],
    layout: 'vertical'
  },
  actions: [
    {
      icon: 'mdi:message',
      tooltip: '发送消息',
      onClick: (row) => { /* 处理消息 */ }
    }
  ],
  enableHover: true
})
```

## 🛠️ 高级用法

### 1. 动态配置

```typescript
// 基于数据动态配置
const dynamicColumn = columnHelper.status('status', '状态', {
  statusMap: computed(() => {
    // 基于某些条件动态生成状态映射
    return generateStatusMap()
  }),
  variant: computed(() => {
    // 动态变体
    return someCondition ? 'badge' : 'dot'
  })
})
```

### 2. 自定义样式

```typescript
const styledColumn = columnHelper.status('priority', '优先级', {
  statusMap: {
    high: {
      text: '高优先级',
      type: 'destructive',
      className: 'font-bold text-red-600',
      style: { backgroundColor: '#fee2e2' }
    }
  }
})
```

### 3. 事件处理

```typescript
const interactiveColumn = columnHelper.composite('product', '产品', {
  main: { field: 'name' },
  actions: [
    {
      icon: 'mdi:heart',
      tooltip: '收藏',
      onClick: (row, column) => {
        // 处理收藏逻辑
        console.log('收藏产品:', row.name)
        
        // 可以触发刷新
        column.refresh?.()
      }
    }
  ]
})
```

## 🔍 调试与优化

### 1. 开发模式调试

```typescript
// 在开发环境中启用详细日志
if (import.meta.env.DEV) {
  // 查看配置存储
  console.log('配置存储:', window.__simpleConfigStore)
  
  // 查看已注册的组件
  console.log('已注册组件:', manager.getRegisteredComponents())
}
```

### 2. 性能优化

- 使用 `computed` 包装计算属性
- 避免在渲染器中进行复杂计算
- 合理使用 `shallowRef` 和 `markRaw`

### 3. 类型安全

```typescript
// 确保完整的类型定义
interface MyRendererConfig extends BaseRendererConfig {
  // 明确的属性类型
  variant: 'type1' | 'type2'
  customData?: Record<string, unknown>
}

// 使用类型守卫
const isValidVariant = (variant: unknown): variant is 'type1' | 'type2' => {
  return typeof variant === 'string' && ['type1', 'type2'].includes(variant)
}
```

## 📝 最佳实践

1. **命名约定**：使用 PascalCase 命名渲染器，如 `MyRenderer`
2. **配置分离**：将复杂逻辑移到 `useRenderer` 函数中
3. **类型完整性**：为所有配置和属性提供完整的 TypeScript 类型
4. **文档完善**：为每个配置属性提供清晰的注释
5. **测试覆盖**：编写单元测试确保渲染器正常工作

## 🚀 快速开始模板

```typescript
// 1. 创建新渲染器目录
mkdir src/components/data-grid/plugins/renderers/my-renderer

// 2. 创建类型文件
// types.ts
export interface MyRendererConfig extends BaseRendererConfig {
  variant?: 'default' | 'custom'
}

export interface MyRendererProps extends BaseRendererProps {
  config?: MyRendererConfig
}

// 3. 创建逻辑文件
// useRenderer.ts
export const useMyRenderer = (props: MyRendererProps) => {
  const value = computed(() => props.field ? props.row?.[props.field] : props.value)
  const variant = computed(() => props.config?.variant || 'default')
  
  return { value, variant }
}

// 4. 创建组件文件
// renderer.vue
<template>
  <div>{{ value }}</div>
</template>

<script setup lang="ts">
defineOptions({ name: 'MyRenderer' })
// ... 组件逻辑
</script>

// 5. 注册到插件系统
// 在 coreRenderers 数组中添加配置
```

这个插件系统设计简洁而强大，支持快速开发和灵活扩展，是构建复杂数据表格的理想选择。