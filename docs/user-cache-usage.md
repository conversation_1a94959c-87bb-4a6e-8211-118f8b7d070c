# 用户缓存工具使用指南

## 概述

用户缓存工具类提供了高效的用户ID转名称功能，通过缓存机制减少API调用，提升系统性能。

## 特性

- ✅ 智能缓存机制，避免重复API调用
- ✅ 自动过期策略（30分钟过期，5分钟强制刷新）
- ✅ 并发请求去重，避免重复加载
- ✅ 支持单个和批量用户查询
- ✅ 支持用户搜索功能
- ✅ Vue 3 Composition API 支持
- ✅ TypeScript 类型支持

## 核心工具类

### UserCache 类

```typescript
import userCache from '@/utils/userCache'

// 根据用户ID获取用户名
const username = await userCache.getUserName(123)

// 根据用户ID获取完整用户信息
const userInfo = await userCache.getUserInfo(123)

// 获取所有用户
const allUsers = await userCache.getAllUsers()

// 根据用户名查找用户ID
const userId = await userCache.getUserIdByUsername('张三')

// 搜索用户
const searchResults = await userCache.searchUsers('张')

// 手动刷新缓存
await userCache.refreshCache()

// 清除缓存
userCache.clearCache()

// 获取缓存统计信息
const stats = userCache.getCacheStats()
```

## Vue 组合式函数

### useUserCache

```vue
<script setup lang="ts">
import { useUserCache } from '@/hooks/useUserCache'

const { getUserName, getUserInfo, getAllUsers, searchUsers, loading, error } =
  useUserCache()

// 获取用户名
const loadUserName = async () => {
  const username = await getUserName(123)
  console.log('用户名:', username)
}

// 搜索用户
const handleSearch = async (keyword: string) => {
  const results = await searchUsers(keyword)
  console.log('搜索结果:', results)
}
</script>
```

### useUserName

```vue
<template>
  <div>
    <span v-if="loading">加载中...</span>
    <span v-else-if="error">{{ error }}</span>
    <span v-else>{{ username || '用户不存在' }}</span>
  </div>
</template>

<script setup lang="ts">
import { useUserName } from '@/hooks/useUserCache'

const { username, loading, error, reload } = useUserName(123)
</script>
```

### useBatchUserNames

```vue
<template>
  <div>
    <div v-for="userId in userIds" :key="userId">
      {{ userNames.get(userId) || '用户不存在' }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { useBatchUserNames } from '@/hooks/useUserCache'

const userIds = [1, 2, 3, 4, 5]
const { userNames, loading, error } = useBatchUserNames(userIds)
</script>
```

## Vue 组件

### UserDisplay 组件

```vue
<template>
  <!-- 单个用户显示 -->
  <UserDisplay :user-id="123" />

  <!-- 批量用户显示 -->
  <UserDisplay :user-ids="[1, 2, 3, 4, 5]" />

  <!-- 带搜索功能 -->
  <UserDisplay :show-search="true" @user-select="handleUserSelect" />
</template>

<script setup lang="ts">
import UserDisplay from '@/components/common/UserDisplay.vue'

const handleUserSelect = (user: { id: number; username: string }) => {
  console.log('选择的用户:', user)
}
</script>
```

## 缓存策略

### 过期时间

- **缓存过期时间**: 30分钟
- **强制刷新时间**: 5分钟

### 缓存机制

1. 首次请求时从API加载数据并缓存
2. 后续请求直接从缓存返回
3. 缓存过期后自动重新加载
4. 支持手动刷新和清除缓存

### 并发控制

- 多个并发请求只会触发一次API调用
- 其他请求会等待第一个请求完成

## 性能优化

### 批量查询

```typescript
// 推荐：批量获取用户信息
const userIds = [1, 2, 3, 4, 5]
const { userNames } = useBatchUserNames(userIds)

// 不推荐：逐个查询
for (const userId of userIds) {
  const username = await userCache.getUserName(userId)
}
```

### 预加载

```typescript
// 在应用启动时预加载用户数据
import userCache from '@/utils/userCache'

// 在路由守卫或应用初始化时
await userCache.getAllUsers()
```

## 错误处理

```typescript
import userCache from '@/utils/userCache'

try {
  const username = await userCache.getUserName(123)
  if (username) {
    console.log('用户名:', username)
  } else {
    console.log('用户不存在')
  }
} catch (error) {
  console.error('获取用户名失败:', error)
  // 处理错误，比如显示默认值
}
```

## 调试和监控

```typescript
// 获取缓存统计信息
const stats = userCache.getCacheStats()
console.log('缓存统计:', {
  总用户数: stats.totalUsers,
  缓存大小: stats.cacheSize,
  最后加载时间: new Date(stats.lastLoadTime),
  是否正在加载: stats.isLoading,
})
```

## 最佳实践

1. **合理使用缓存**: 不要频繁清除缓存，让缓存机制自动管理
2. **批量操作**: 优先使用批量查询而不是逐个查询
3. **错误处理**: 始终处理可能的错误情况
4. **预加载**: 在应用启动时预加载常用数据
5. **监控性能**: 定期检查缓存统计信息

## 注意事项

1. 缓存数据可能与后端数据不同步，需要时请手动刷新
2. 用户数据变更后，建议调用 `refreshCache()` 更新缓存
3. 在用户登出时，可以调用 `clearCache()` 清除缓存
4. 缓存使用内存存储，大量用户数据可能占用较多内存
