import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type {
  Employee,
  EmployeeCreate,
  EmployeeUpdate,
} from './types';

/**
 * @description 获取Employee元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getEmployeeMetadata = () => {
  return alovaInstance.Get<any>('/v1/hr/employee/get_metadata');
};

/**
 * @description 获取Employee列表
 * @param {EmployeeQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Employee>>} 返回包含Employee信息的Promise对象
 * @example
 * // 使用示例
 * const employeeList = await getEmployeeList({ start: 1, limit: 20 });
 */
const getEmployeeList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<Employee>>('/v1/hr/employee/query', params);
};

/**
 * @description 获取Employee详情
 * @param {number} id Employee ID
 * @returns {Promise<Employee>} 返回Employee详情信息
 */
const getEmployee = (id: number, max_depth: number = 1) => {
  return alovaInstance.Get<Employee>('/v1/hr/employee/get', {
    params: {
      id: id,
      max_depth: max_depth,
    },
  });
};

/**
 * @description 创建Employee
 * @param {EmployeeCreate} data 创建数据
 * @returns {Promise<Employee>} 返回创建的Employee信息
 */
const createEmployee = (data: EmployeeCreate) => {
  return alovaInstance.Post<Employee>('/v1/hr/employee/create', data);
};

/**
 * @description 更新Employee
 * @param {EmployeeUpdate} data 更新数据
 * @returns {Promise<Employee>} 返回更新后的Employee信息
 */
const updateEmployee = (data: EmployeeUpdate) => {
  return alovaInstance.Put<Employee>('/v1/hr/employee/update', data);
};

/**
 * @description 删除Employee
 * @param {number} id Employee ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeEmployee = (id: number) => {
  return alovaInstance.Delete<any>(`/v1/hr/employee/delete/${id}`);
};

/**
 * @description 批量删除Employee
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const bulkDeleteEmployee = (ids: number[]) => {
  return alovaInstance.Delete<any>('/hr/employee/bulk_delete', ids);
};

// /**
//  * @description 导出Employee数据
//  * @param {EmployeeQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportEmployee = (params?: EmployeeQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/hr/employee/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Employee数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importEmployee = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/hr/employee/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
  getEmployeeMetadata,
  getEmployeeList,
  getEmployee,
  createEmployee,
  updateEmployee,
  removeEmployee,
  bulkDeleteEmployee,
  // exportEmployee,
  // importEmployee,
};