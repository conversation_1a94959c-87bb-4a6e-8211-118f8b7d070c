import type { ModelBase } from "@/types/core"

/**
 * 性别
 */
export enum SexEnum {
  MALE = '男',
  FEMALE = '女',
  UNKNOWN = '未知'
}

// Employee 数据接口定义
export interface Employee extends ModelBase {
  id: number;
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  factory_id?: number; // 所属组织
  employee_no?: string; // 员工工号
  nick_name?: string; // 员工名称
  dept_id?: number; // 所属部门
  post_id?: number; // 所属岗位
  employee_type_id?: number; // 用工类型
  employee_state_id?: number; // 人员状态
  photo?: string; // 照片
  entry_date?: string; // 入职时间
  worker_date?: string; // 转正时间
  probation?: number; // 试用期（月）
  salary?: string; // 薪资
  other_benefit?: string; // 其他待遇
  bank?: string; // 所属银行
  bank_card_no?: string; // 银行卡号
  term_date?: string; // 离职时间
  sex?: SexEnum; // 性别
  id_card_no?: string; // 身份证号
  age?: number; // 年龄
  qualification_id?: number; // 学历
  graduate_school?: string; // 毕业学校
  nationality?: string; // 国籍
  nation?: string; // 名族
  native_place?: string; // 籍贯
  account_address?: string; // 户籍地址
  phone_number?: string; // 联系电话
  mailbox?: string; // 邮箱
  emergency_contact_name?: string; // 紧急联系人
  emergency_contact_phone?: string; // 联系电话
  relationship?: string; // 与其关系
  is_user: boolean; // 是否用户
  dept?: Dept; // dept
  post?: DictData; // post
  employee_type?: DictData; // employee_type
  employee_state?: DictData; // employee_state
  qualification?: DictData; // qualification
}

export interface EmployeeCreate extends Omit<Employee, 'id'> {

}

export interface EmployeeUpdate extends Employee {

}

// API 响应接口定义
