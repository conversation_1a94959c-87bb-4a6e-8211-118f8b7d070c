/**
 * 关联模型映射配置
 *
 * 将后端 metadata 中的 related_model 映射到前端 API 服务的 moduleModel 路径
 * 用于根据关联模型名称获取对应的 API 服务实例
 */

import type { RelationModelMapping } from '../components/data-grid/plugins/renderers/relation/types'

/**
 * 关联模型到 API 模块路径的映射表
 *
 * 格式: { "关联模型名": "模块/模型路径" }
 *
 * 示例:
 * - "Employee" -> "hr/employee"
 * - "DemoItem" -> "demo/demo_item"
 */
export const relationModelMap: RelationModelMapping = {
  // ======== 人力资源模块 ========
  Employee: 'hr/employee',
  Department: 'hr/department',
  Position: 'hr/position',
  Team: 'hr/team',

  // ======== 销售模块 ========
  Customer: 'sal/customer',
  SalesOrder: 'sal/sales_order',
  SalesQuotation: 'sal/sales_quotation',
  SalesContract: 'sal/sales_contract',
  SalesDelivery: 'sal/sales_delivery',
  SalesReturn: 'sal/sales_return',

  // ======== Demo 模块 ========
  Demo: 'demo/demo',
  DemoItem: 'demo/demo_item',

  // ======== 采购模块 ========
  Supplier: 'scr/supplier',
  PurchaseApply: 'ops/purchase_apply',
  PurchaseOrder: 'ops/purchase_order',
  PurchaseReceipt: 'ops/purchase_receipt',
  PurchaseReturn: 'ops/purchase_return',
  PurchaseContract: 'ops/purchase_contract',

  // ======== 库存/仓储模块 ========
  Material: 'ops/material',
  Product: 'ops/product',
  InventoryLocationInfo: 'wms/inventory_location_info',
  InventoryInfo: 'wms/inventory_info',
  Inbound: 'wms/inbound',
  Outbound: 'wms/outbound',
  Adjustment: 'wms/adjustment',
  Return: 'wms/return',
  WmsReserve: 'wms/wms_reserve',
  WmsGoodsShelves: 'wms/wms_goods_shelves',

  // ======== 财务模块 ========
  FinReceivable: 'fin/receivable',
  FinCollection: 'fin/collection',
  FinPayable: 'fin/payable',
  FinPayment: 'fin/payment',
  FinAccountingEntry: 'fin/accounting_entry',
  FinVoucher: 'fin/voucher',
  FinAnalysis: 'fin/analysis',
  FinBudget: 'fin/budget',
  FinForecast: 'fin/forecast',

  // ======== 生产制造模块 ========
  MesProductionNotice: 'mes/production_notice',
  MesFlowCard: 'mes/flow_card',
  MesOptimization: 'mes/optimization',
  MesMaterialRequisition: 'mes/material_requisition',

  // ======== 基础数据模块 ========
  BasWorkmanshipRoute: 'bas/workmanship_route',

  // ======== 运营模块 ========
  OpsProject: 'ops/project',
  OpsBom: 'ops/bom',
  OpsProjectQuotation: 'ops/project_quotation',
  OpsProjectContract: 'ops/project_contract',
  OpsProjectDesign: 'ops/project_design',

  // ======== 系统模块 ========
  User: 'sys/user',
  Role: 'sys/role',
  Permission: 'sys/permission',
  Organization: 'sys/organization',
}

/**
 * 获取关联模型对应的 API 模块路径
 * @param relatedModel 关联模型名称，如 "Employee", "DemoItem"
 * @returns API 模块路径，如 "hr/employee", "demo/demo_item"，如果未找到返回 null
 *
 * @example
 * ```typescript
 * const moduleModel = getModuleModelPath("Employee")
 * console.log(moduleModel) // "hr/employee"
 *
 * const api = await getApi(moduleModel)
 * const detail = await api.getDetail(employeeId)
 * ```
 */
export function getModuleModelPath(relatedModel: string): string | null {
  const moduleModel = relationModelMap[relatedModel]

  if (!moduleModel) {
    console.warn(
      `[RelationModelMap] 未找到关联模型 "${relatedModel}" 的 API 映射配置`
    )
    return null
  }

  return moduleModel
}

/**
 * 动态添加关联模型映射
 * 用于运行时扩展映射表，支持插件化开发
 *
 * @param relatedModel 关联模型名称
 * @param moduleModel API 模块路径
 *
 * @example
 * ```typescript
 * // 添加新的关联模型映射
 * addRelationModelMapping("CustomModel", "custom/custom_model")
 * ```
 */
export function addRelationModelMapping(
  relatedModel: string,
  moduleModel: string
): void {
  if (!relatedModel || !moduleModel) {
    console.error('[RelationModelMap] 关联模型名称和模块路径不能为空')
    return
  }

  if (
    relationModelMap[relatedModel] &&
    relationModelMap[relatedModel] !== moduleModel
  ) {
    console.warn(
      `[RelationModelMap] 关联模型 "${relatedModel}" 已存在映射 "${relationModelMap[relatedModel]}"，将被覆盖为 "${moduleModel}"`
    )
  }

  relationModelMap[relatedModel] = moduleModel
  console.log(
    `[RelationModelMap] 已添加映射: ${relatedModel} -> ${moduleModel}`
  )
}

/**
 * 批量添加关联模型映射
 * @param mappings 映射配置对象
 *
 * @example
 * ```typescript
 * addRelationModelMappings({
 *   "CustomModel1": "custom/model1",
 *   "CustomModel2": "custom/model2"
 * })
 * ```
 */
export function addRelationModelMappings(mappings: RelationModelMapping): void {
  Object.entries(mappings).forEach(([relatedModel, moduleModel]) => {
    addRelationModelMapping(relatedModel, moduleModel)
  })
}

/**
 * 检查关联模型是否已配置映射
 * @param relatedModel 关联模型名称
 * @returns 是否已配置
 */
export function hasRelationModelMapping(relatedModel: string): boolean {
  return relatedModel in relationModelMap
}

/**
 * 获取所有已配置的关联模型列表
 * @returns 关联模型名称数组
 */
export function getAllRelatedModels(): string[] {
  return Object.keys(relationModelMap)
}

/**
 * 获取所有已配置的 API 模块路径列表
 * @returns API 模块路径数组
 */
export function getAllModuleModels(): string[] {
  return Object.values(relationModelMap)
}

/**
 * 验证映射表完整性
 * 检查是否有重复的模块路径映射
 * @returns 验证结果
 */
export function validateRelationModelMap(): {
  isValid: boolean
  duplicates: Array<{ moduleModel: string; relatedModels: string[] }>
  warnings: string[]
} {
  const moduleModelMap = new Map<string, string[]>()
  const warnings: string[] = []

  // 检查重复映射
  Object.entries(relationModelMap).forEach(([relatedModel, moduleModel]) => {
    if (!moduleModelMap.has(moduleModel)) {
      moduleModelMap.set(moduleModel, [])
    }
    moduleModelMap.get(moduleModel)!.push(relatedModel)
  })

  // 找出重复的模块路径
  const duplicates = Array.from(moduleModelMap.entries())
    .filter(([, relatedModels]) => relatedModels.length > 1)
    .map(([moduleModel, relatedModels]) => ({ moduleModel, relatedModels }))

  // 生成警告信息
  duplicates.forEach(({ moduleModel, relatedModels }) => {
    warnings.push(
      `模块路径 "${moduleModel}" 被多个关联模型使用: ${relatedModels.join(', ')}`
    )
  })

  return {
    isValid: duplicates.length === 0,
    duplicates,
    warnings,
  }
}

// 开发环境下验证映射表
if (import.meta.env.DEV) {
  const validation = validateRelationModelMap()
  if (!validation.isValid) {
    console.warn('[RelationModelMap] 映射表验证失败:', validation.warnings)
  } else {
    console.log(
      '[RelationModelMap] 映射表验证通过，共配置',
      getAllRelatedModels().length,
      '个关联模型'
    )
  }
}
