import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type {
  OrderItem,
  OrderItemCreate,
  OrderItemUpdate,
} from './types';

/**
 * @description 获取OrderItem元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getOrderItemMetadata = () => {
  return alovaInstance.Get<any>('/v1/sal/order_item/get_metadata');
};

/**
 * @description 获取OrderItem列表
 * @param {OrderItemQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<OrderItem>>} 返回包含OrderItem信息的Promise对象
 * @example
 * // 使用示例
 * const orderItemList = await getOrderItemList({ start: 1, limit: 20 });
 */
const getOrderItemList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<OrderItem>>('/v1/sal/order_item/query', params);
};

/**
 * @description 获取OrderItem详情
 * @param {number} id OrderItem ID
 * @returns {Promise<OrderItem>} 返回OrderItem详情信息
 */
const getOrderItem = (id: number, max_depth: number = 1) => {
  return alovaInstance.Get<OrderItem>('/v1/sal/order_item/get', {
    params: {
      id: id,
      max_depth: max_depth,
    },
  });
};

/**
 * @description 创建OrderItem
 * @param {OrderItemCreate} data 创建数据
 * @returns {Promise<OrderItem>} 返回创建的OrderItem信息
 */
const createOrderItem = (data: OrderItemCreate) => {
  return alovaInstance.Post<OrderItem>('/v1/sal/order_item/create', data);
};

/**
 * @description 更新OrderItem
 * @param {OrderItemUpdate} data 更新数据
 * @returns {Promise<OrderItem>} 返回更新后的OrderItem信息
 */
const updateOrderItem = (data: OrderItemUpdate) => {
  return alovaInstance.Put<OrderItem>('/v1/sal/order_item/update', data);
};

/**
 * @description 删除OrderItem
 * @param {number} id OrderItem ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeOrderItem = (id: number) => {
  return alovaInstance.Delete<any>(`/v1/sal/order_item/delete/${id}`);
};

/**
 * @description 批量删除OrderItem
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const bulkDeleteOrderItem = (ids: number[]) => {
  return alovaInstance.Delete<any>('/sal/order_item/bulk_delete', ids);
};

// /**
//  * @description 导出OrderItem数据
//  * @param {OrderItemQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportOrderItem = (params?: OrderItemQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/sal/order_item/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入OrderItem数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importOrderItem = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/sal/order_item/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
  getOrderItemMetadata,
  getOrderItemList,
  getOrderItem,
  createOrderItem,
  updateOrderItem,
  removeOrderItem,
  bulkDeleteOrderItem,
  // exportOrderItem,
  // importOrderItem,
};