import type { ModelBase } from "@/types/core"

/**
 * 销售类型
 */
export enum SaleType {
  Product = '产品销售',
  Material = '材料销售'
}

/**
 * 计价方式
 */
export enum PricingMode {
  Area = '按面积计价',
  Number = '按数量计价',
  Girth = '按周长计价',
  Weight = '按重量计价'
}

/**
 * 弯钢方向
 */
export enum BendDirection {
  OUTER = '外弯',
  INNER = '内弯'
}

// OrderItem 数据接口定义
export interface OrderItem extends ModelBase {
  id: number;
  seq_no: number; // 产品序号
  sale_type: SaleType; // 销售类型
  product_id?: number; // 产品
  code?: string; // 产品编码
  name?: string; // 产品名称
  alias?: string; // 产品别名
  specs?: string; // 规格型号
  width?: number; // 宽
  height?: number; // 高
  big_or_small?: number; // 大头/小头
  pricing_mode: PricingMode; // 计价方式
  qty: number; // 数量
  price: number; // 单价
  sales_area?: number; // 单件结算面积
  material_cost: number; // 材料费
  wip_cost?: number; // 工艺费
  discount_rate?: number; // 折扣率
  discount_amount?: number; // 折扣金额
  total_amount?: number; // 总金额
  shape_id?: number; // 形状
  color_id?: number; // 颜色
  uom_id?: number; // 单位
  work_content?: string; // 加工内容
  glue_depth?: number; // 胶深
  item?: string; // 项目
  notes?: string; // 备注
  bend_direction?: BendDirection; // 弯钢方向
  actual_area?: number; // 单件实际面积
  total_actual_area?: number; // 实际总面积
  total_sales_area?: number; // 结算总面积
  girth?: number; // 周长
  total_girth?: number; // 总周长
  weight?: number; // 单件重量
  total_weight?: number; // 总重量
  product_deep?: number; // 产品厚度
  rm_deep?: number; // 原料厚度
  al_bar_deep?: number; // 铝条厚度
  film_deep?: number; // 胶片厚度
  org_id?: number; // 源单Id
  order_id?: number; // 订单Id
  order_no?: string; // 订单单号
  is_optimize?: boolean; // 是否已优化
  invoiced_qty?: number; // 开票数量
  delivery_qty?: number; // 已送数量
  arrears_qty?: number; // 送货欠数
  return_qty?: number; // 退货数量
  inbound_qty?: number; // 入库数量
  pack_qty?: number; // 分箱数量
  mini_sales_area?: number; // 单件最小结算面积
  bom_id?: number; // BOMid
  bom_name?: string; // BOM名称
  produced_qty: number; // 已生产数量
  un_produced_qty: number; // 未生产数量
  pic?: string; // 图片
  workmanship_route_id?: number; // 工艺路线id
  window_no?: string; // 窗号
  floor?: string; // 楼层
  wip_costs: OrderWipCost[]; // wip_costs
  pro_sets: OrderProSet[]; // pro_sets
  workmanship_route?: WorkmanshipRoute; // workmanship_route
  uom?: DictData; // uom
  shape?: DictData; // shape
  color?: DictData; // color
  is_complete_delivery: boolean; // 是否已完成送货
}

export interface OrderItemCreate extends Omit<OrderItem, 'id'> {

}

export interface OrderItemUpdate extends OrderItem {

}

// API 响应接口定义
