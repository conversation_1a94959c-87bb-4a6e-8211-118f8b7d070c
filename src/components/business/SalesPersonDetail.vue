<template>
  <div class="sales-person-detail p-4">
    <div class="flex items-center space-x-4 mb-6">
      <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
        <span class="text-2xl font-bold text-blue-600">
          {{ getInitials(data?.nick_name || data?.name) }}
        </span>
      </div>
      <div>
        <h2 class="text-xl font-semibold">{{ data?.nick_name || data?.name || '未知销售员' }}</h2>
        <p class="text-gray-600">员工编号: {{ data?.employee_no || '--' }}</p>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 基本信息 -->
      <div class="bg-gray-50 p-4 rounded-lg">
        <h3 class="text-lg font-medium mb-3">基本信息</h3>
        <div class="space-y-2">
          <div class="flex justify-between">
            <span class="text-gray-600">姓名:</span>
            <span>{{ data?.name || '--' }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">昵称:</span>
            <span>{{ data?.nick_name || '--' }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">员工编号:</span>
            <span>{{ data?.employee_no || '--' }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">部门:</span>
            <span>{{ data?.department || '--' }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">职位:</span>
            <span>{{ data?.position || '--' }}</span>
          </div>
        </div>
      </div>

      <!-- 联系信息 -->
      <div class="bg-gray-50 p-4 rounded-lg">
        <h3 class="text-lg font-medium mb-3">联系信息</h3>
        <div class="space-y-2">
          <div class="flex justify-between">
            <span class="text-gray-600">邮箱:</span>
            <span>{{ data?.email || '--' }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">电话:</span>
            <span>{{ data?.phone || '--' }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">地址:</span>
            <span>{{ data?.address || '--' }}</span>
          </div>
        </div>
      </div>

      <!-- 业绩统计 -->
      <div class="bg-blue-50 p-4 rounded-lg">
        <h3 class="text-lg font-medium mb-3">业绩统计</h3>
        <div class="space-y-2">
          <div class="flex justify-between">
            <span class="text-gray-600">本月销售额:</span>
            <span class="font-semibold text-blue-600">
              ¥{{ formatCurrency(data?.monthly_sales || 0) }}
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">本年销售额:</span>
            <span class="font-semibold text-blue-600">
              ¥{{ formatCurrency(data?.yearly_sales || 0) }}
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">客户数量:</span>
            <span>{{ data?.customer_count || 0 }}</span>
          </div>
        </div>
      </div>

      <!-- 其他信息 -->
      <div class="bg-gray-50 p-4 rounded-lg">
        <h3 class="text-lg font-medium mb-3">其他信息</h3>
        <div class="space-y-2">
          <div class="flex justify-between">
            <span class="text-gray-600">入职时间:</span>
            <span>{{ formatDate(data?.hire_date) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">状态:</span>
            <span :class="getStatusClass(data?.status)">
              {{ getStatusText(data?.status) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-end space-x-2 mt-6 pt-4 border-t">
      <button
        class="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
        @click="handleEdit"
      >
        编辑
      </button>
      <button
        class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        @click="handleViewDetails"
      >
        查看详情
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  data: any
  row?: any
}

const props = defineProps<Props>()

// 获取姓名首字母
const getInitials = (name: string) => {
  if (!name) return '?'
  return name.charAt(0).toUpperCase()
}

// 格式化货币
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-CN').format(amount)
}

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '--'
  return new Date(date).toLocaleDateString('zh-CN')
}

// 获取状态样式
const getStatusClass = (status: string) => {
  switch (status) {
    case 'active':
      return 'text-green-600 font-medium'
    case 'inactive':
      return 'text-red-600 font-medium'
    default:
      return 'text-gray-600'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'active':
      return '在职'
    case 'inactive':
      return '离职'
    default:
      return '未知'
  }
}

// 处理编辑
const handleEdit = () => {
  console.log('编辑销售员:', props.data)
  // 这里可以触发编辑表单
}

// 处理查看详情
const handleViewDetails = () => {
  console.log('查看销售员详情:', props.data)
  // 这里可以跳转到详情页面
}
</script>

<style scoped>
.sales-person-detail {
  max-width: 800px;
  margin: 0 auto;
}
</style>
