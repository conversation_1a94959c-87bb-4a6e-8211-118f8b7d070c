<template>
    <!-- 使用 v-model:open 实现双向绑定 -->
    <Dialog v-model:open="internalVisible">
        <!-- 触发器 -->
        <DialogTrigger v-if="slots?.trigger">
            <slot name="trigger"></slot>
        </DialogTrigger>
        <DialogContent class="max-w-[100%] grid-rows-[auto_1fr]" :class="sizeClass" :zIndex="props.zIndex">
            <DialogHeader>
                <!-- 标题 -->
                <DialogTitle v-if="slots?.title">
                    <slot name="title"></slot>
                </DialogTitle>
                <DialogTitle v-else class="text-title">{{
                    props.title
                }}</DialogTitle>
                <DialogDescription v-if="props.description">{{
                    props.description
                }}</DialogDescription>
            </DialogHeader>
            <slot></slot>

            <!-- 底部 -->
            <DialogFooter v-if="slots?.footer">
                <slot name="footer"></slot>
            </DialogFooter>
        </DialogContent>
    </Dialog>
</template>

<script setup lang="ts">
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import { computed, useSlots, watch, ref } from 'vue';

type Size = 'large' | 'middle' | 'small' | 'mini';
// Dialog组件的Size类型

// Dialog组件的Props类型定义
interface DialogProps {
    /** 控制显隐 */
    visible?: boolean;
    /** 弹窗尺寸 */
    size?: Size;
    /** 标题 */
    title?: string;
    /** 弹窗描述 */
    description?: string;
    /** 底部按钮 */
    footer?: string;
    /** 弹窗层级 */
    zIndex?: number;
}
const props = withDefaults(defineProps<DialogProps>(), {
    visible: false,
    size: 'small',
    showClose: true,
    zIndex: 45,
});

// 定义 emits，包括 update:visible 事件
const emit = defineEmits<{
    close: [];
    'update:visible': [value: boolean];
}>();

// 内部状态管理
const internalVisible = ref(props.visible);

// 监听 props.visible 的变化，同步到内部状态
watch(() => props.visible, (newValue) => {
    internalVisible.value = newValue;
});

// 监听内部状态的变化，向外发射事件
watch(internalVisible, (newValue) => {
    emit('update:visible', newValue);
    if (!newValue) {
        emit('close');
    }
});

const sizeClass = computed(() => {
    return {
        'w-[90%]': props.size === 'large',
        'w-[70%]': props.size === 'middle',
        'w-[50%]': props.size === 'small',
        'w-[30%]': props.size === 'mini',
        'min-h-[60vh]': props.size !== 'mini',
    };
});

// 接收插槽数据
const slots = useSlots();

</script>
