<template>
  <div class="user-display">
    <!-- 单个用户显示 -->
    <div v-if="userId" class="user-item">
      <span v-if="loading" class="loading">加载中...</span>
      <span v-else-if="error" class="error">{{ error }}</span>
      <span v-else-if="username" class="username">{{ username }}</span>
      <span v-else class="not-found">用户不存在</span>
    </div>

    <!-- 批量用户显示 -->
    <div v-if="userIds && userIds.length > 0" class="user-list">
      <div v-for="userId in userIds" :key="userId" class="user-item">
        <span v-if="loading" class="loading">加载中...</span>
        <span v-else-if="error" class="error">{{ error }}</span>
        <span v-else-if="userNames.get(userId)" class="username">
          {{ userNames.get(userId) }}
        </span>
        <span v-else class="not-found">用户不存在</span>
      </div>
    </div>

    <!-- 用户搜索 -->
    <div v-if="showSearch" class="user-search">
      <input
        v-model="searchKeyword"
        @input="handleSearch"
        placeholder="搜索用户..."
        class="search-input"
      />
      <div v-if="searchResults.length > 0" class="search-results">
        <div
          v-for="user in searchResults"
          :key="user.id"
          class="search-result-item"
          @click="selectUser(user)"
        >
          {{ user.username }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  useUserName,
  useBatchUserNames,
  useUserCache,
} from '@/hooks/useUserCache'

interface Props {
  // 单个用户ID
  userId?: number | string
  // 批量用户ID
  userIds?: (number | string)[]
  // 是否显示搜索功能
  showSearch?: boolean
  // 是否显示加载状态
  showLoading?: boolean
}

interface Emits {
  (e: 'userSelect', user: { id: number; username: string }): void
}

const props = withDefaults(defineProps<Props>(), {
  showSearch: false,
  showLoading: true,
})

const emit = defineEmits<Emits>()

// 搜索相关
const searchKeyword = ref('')
const searchResults = ref<Array<{ id: number; username: string }>>([])
const { searchUsers } = useUserCache()

// 单个用户显示
const { username, loading, error, reload } = useUserName(
  computed(() => props.userId)
)

// 批量用户显示
const {
  userNames,
  loading: batchLoading,
  error: batchError,
} = useBatchUserNames(computed(() => props.userIds || []))

// 处理搜索
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    searchResults.value = []
    return
  }

  try {
    const results = await searchUsers(searchKeyword.value)
    searchResults.value = results.map((user) => ({
      id: user.id,
      username: user.username,
    }))
  } catch (error) {
    console.error('搜索用户失败:', error)
    searchResults.value = []
  }
}

// 选择用户
const selectUser = (user: { id: number; username: string }) => {
  emit('userSelect', user)
  searchKeyword.value = ''
  searchResults.value = []
}

// 监听用户ID变化，重新加载
watch(
  () => props.userId,
  () => {
    if (props.userId) {
      reload()
    }
  }
)

// 暴露方法给父组件
defineExpose({
  reload,
  searchUsers: handleSearch,
})
</script>

<style scoped>
.user-display {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-item {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.user-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.loading {
  color: #666;
  font-style: italic;
}

.error {
  color: #ff4d4f;
}

.username {
  color: #1890ff;
  font-weight: 500;
}

.not-found {
  color: #999;
  font-style: italic;
}

.user-search {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.search-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.search-result-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-result-item:hover {
  background-color: #f5f5f5;
}

.search-result-item:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
}
</style>
