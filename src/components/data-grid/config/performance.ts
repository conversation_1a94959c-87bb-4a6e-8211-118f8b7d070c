/**
 * DataGrid 性能优化配置
 */

export interface PerformanceConfig {
  // 数据相关配置
  data: {
    /** 分页大小建议（大于此值会显示性能警告） */
    recommendedPageSize: number
    /** 最大分页大小 */
    maxPageSize: number
    /** 启用虚拟滚动的最小行数 */
    virtualScrollThreshold: number
  }

  // 渲染相关配置
  render: {
    /** 启用列缓存 */
    enableColumnCache: boolean
    /** 启用组件缓存 */
    enableComponentCache: boolean
    /** 防抖延迟（毫秒） */
    debounceDelay: number
  }

  // 监控相关配置
  monitoring: {
    /** 启用性能监控 */
    enabled: boolean
    /** 监控采样间隔（毫秒） */
    sampleInterval: number
    /** 性能警告阈值（毫秒） */
    warningThreshold: number
    /** 是否在控制台输出性能报告 */
    logToConsole: boolean
  }

  // 内存相关配置
  memory: {
    /** 缓存最大条目数 */
    maxCacheEntries: number
    /** 缓存过期时间（毫秒） */
    cacheExpiration: number
    /** 启用自动清理 */
    autoCleanup: boolean
  }
}

export const DEFAULT_PERFORMANCE_CONFIG: PerformanceConfig = {
  data: {
    recommendedPageSize: 30, // 从50降到30，更严格的性能要求
    maxPageSize: 100, // 从200降到100，减少内存压力
    virtualScrollThreshold: 50, // 从100降到50，更早启用虚拟滚动
  },
  render: {
    enableColumnCache: true,
    enableComponentCache: true,
    debounceDelay: 150, // 从300ms降到150ms，提高响应性
  },
  monitoring: {
    enabled: import.meta.env.DEV,
    sampleInterval: 5000, // 从2秒改为5秒，进一步减少开销
    warningThreshold: 500, // 从1000ms降到500ms，更早发现问题
    logToConsole: false, // 默认关闭，减少输出开销
  },
  memory: {
    maxCacheEntries: 30, // 从50降到30，减少内存占用
    cacheExpiration: 3 * 60 * 1000, // 从5分钟降到3分钟
    autoCleanup: true,
  },
}

/**
 * 动态虚拟滚动配置
 */
export interface DynamicVirtualScrollConfig {
  /** 根据列数动态调整阈值 */
  getThreshold: (columnCount: number) => number
  /** 分层渲染策略 */
  renderStrategy: {
    immediate: number // 立即渲染的行数
    buffered: number // 缓冲区行数
    lazy: number // 懒加载行数
  }
}

export const DYNAMIC_VIRTUAL_SCROLL_CONFIG: DynamicVirtualScrollConfig = {
  getThreshold: (columnCount: number) => {
    if (columnCount <= 3) return 100 // 少列：更高阈值
    if (columnCount <= 6) return 75 // 中等列数：中等阈值
    if (columnCount <= 10) return 50 // 较多列：当前阈值
    if (columnCount <= 15) return 30 // 多列：更低阈值
    return 20 // 超多列：最低阈值
  },

  renderStrategy: {
    immediate: 20, // 立即渲染的行数
    buffered: 50, // 缓冲区行数
    lazy: 100, // 懒加载行数
  },
}

/**
 * 获取性能配置
 */
export function getPerformanceConfig(): PerformanceConfig {
  return DEFAULT_PERFORMANCE_CONFIG
}

/**
 * 获取动态虚拟滚动阈值
 */
export function getDynamicVirtualScrollThreshold(
  columnCount: number,
  dataCount: number
): number {
  const baseThreshold = DYNAMIC_VIRTUAL_SCROLL_CONFIG.getThreshold(columnCount)

  // 根据数据量进一步调整
  if (dataCount > 1000) {
    return Math.max(baseThreshold * 0.5, 10) // 大数据量时降低阈值
  } else if (dataCount > 500) {
    return Math.max(baseThreshold * 0.7, 15) // 中等数据量时适度降低
  }

  return baseThreshold
}

/**
 * 性能检查器
 */
export class PerformanceChecker {
  private config: PerformanceConfig

  constructor(config: PerformanceConfig = DEFAULT_PERFORMANCE_CONFIG) {
    this.config = config
  }

  /**
   * 检查数据大小是否合理
   */
  checkDataSize(
    rowCount: number,
    columnCount: number
  ): {
    isOptimal: boolean
    warnings: string[]
    suggestions: string[]
  } {
    const warnings: string[] = []
    const suggestions: string[] = []

    // 检查行数 - 使用动态阈值
    const dynamicThreshold = getDynamicVirtualScrollThreshold(
      columnCount,
      rowCount
    )

    if (rowCount > this.config.data.recommendedPageSize) {
      warnings.push(
        `数据行数 (${rowCount}) 超过建议值 (${this.config.data.recommendedPageSize})`
      )
      suggestions.push('考虑减少分页大小或启用虚拟滚动')
    }

    if (rowCount > this.config.data.maxPageSize) {
      warnings.push(
        `数据行数 (${rowCount}) 超过最大限制 (${this.config.data.maxPageSize})`
      )
      suggestions.push('请减少分页大小以获得更好的性能')
    }

    // 动态虚拟滚动建议
    if (rowCount > dynamicThreshold) {
      warnings.push(
        `当前列数 (${columnCount}) 下，建议在 ${dynamicThreshold} 行以上启用虚拟滚动`
      )
      suggestions.push(`当前数据量 (${rowCount} 行) 建议启用虚拟滚动以提升性能`)
    }

    // 检查列数 - 更严格的标准
    if (columnCount > 15) {
      warnings.push(`列数 (${columnCount}) 较多，可能影响渲染性能`)
      suggestions.push('考虑隐藏部分列或使用列固定功能')
    } else if (columnCount > 10) {
      suggestions.push(`列数 (${columnCount}) 偏多，建议优化列配置`)
    }

    // 检查总数据量 - 更严格的标准
    const totalCells = rowCount * columnCount
    if (totalCells > 5000) {
      warnings.push(`总数据量 (${totalCells} 个单元格) 较大`)
      suggestions.push('启用虚拟滚动或优化列配置')
    } else if (totalCells > 2000) {
      suggestions.push(`总数据量 (${totalCells} 个单元格) 偏大，建议优化`)
    }

    return {
      isOptimal: warnings.length === 0,
      warnings,
      suggestions,
    }
  }

  /**
   * 检查渲染时间
   */
  checkRenderTime(renderTime: number): {
    isOptimal: boolean
    level: 'good' | 'warning' | 'critical'
    message: string
  } {
    // 更严格的性能标准
    if (renderTime < 50) {
      return {
        isOptimal: true,
        level: 'good',
        message: `渲染时间 ${renderTime.toFixed(2)}ms - 优秀`,
      }
    } else if (renderTime < 100) {
      return {
        isOptimal: true,
        level: 'warning',
        message: `渲染时间 ${renderTime.toFixed(2)}ms - 良好`,
      }
    } else if (renderTime < this.config.monitoring.warningThreshold) {
      return {
        isOptimal: false,
        level: 'warning',
        message: `渲染时间 ${renderTime.toFixed(2)}ms - 需要关注`,
      }
    } else {
      return {
        isOptimal: false,
        level: 'critical',
        message: `渲染时间 ${renderTime.toFixed(2)}ms - 需要优化`,
      }
    }
  }
}

/**
 * 全局性能检查器实例
 */
export const performanceChecker = new PerformanceChecker()
