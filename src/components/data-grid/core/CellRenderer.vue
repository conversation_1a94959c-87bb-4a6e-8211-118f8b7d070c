<template>
  <component
    v-if="shouldRenderComponent"
    :is="slotInfo.config.component"
    v-bind="componentProps"
    :key="componentKey"
  />
  <div
    v-else-if="shouldRenderHtml"
    v-html="htmlContent"
  />
  <span v-else>{{ fallbackContent }}</span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  slotInfo: {
    config?: {
      component?: any
    }
  }
  params: {
    rowIndex?: number
    [key: string]: any
  }
  isDestroying: boolean
  renderSlotWithConfig?: (slotInfo: any, params: any) => string
  getComponentProps?: (slotInfo: any, params: any) => Record<string, any>
}

const props = defineProps<Props>()

// 计算是否应该渲染组件
const shouldRenderComponent = computed(() => {
  return !props.isDestroying && 
         props.slotInfo.config?.component &&
         props.getComponentProps
})

// 计算是否应该渲染 HTML
const shouldRenderHtml = computed(() => {
  return !props.isDestroying &&
         !props.slotInfo.config?.component &&
         props.slotInfo &&
         props.params &&
         props.renderSlotWithConfig
})

// 计算组件属性
const componentProps = computed(() => {
  if (!shouldRenderComponent.value || !props.getComponentProps) {
    return {}
  }
  return props.getComponentProps(props.slotInfo, props.params)
})

// 计算组件键，用于强制重新渲染
const componentKey = computed(() => {
  return `comp-${props.params.rowIndex || 0}`
})

// 计算 HTML 内容
const htmlContent = computed(() => {
  if (!shouldRenderHtml.value || !props.renderSlotWithConfig) {
    return ''
  }
  return props.renderSlotWithConfig(props.slotInfo, props.params)
})

// 计算回退内容
const fallbackContent = computed(() => {
  if (props.isDestroying) return ''
  return props.params?.value || '-'
})
</script>
