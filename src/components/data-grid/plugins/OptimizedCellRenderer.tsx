/**
 * 优化版组件渲染器 - 减少组件实例化开销
 *
 * 主要优化：
 * 1. 虚拟滚动支持
 * 2. 组件实例复用
 * 3. 渲染批处理
 * 4. 懒加载渲染
 */

import {
  defineComponent,
  ref,
  computed,
  onMounted,
  onBeforeUnmount,
  nextTick,
  shallowRef,
  h,
} from 'vue'
import type { PropType } from 'vue'

interface RenderItem {
  id: string
  componentName: string
  props: any
  visible: boolean
  rendered: boolean
}

interface VirtualScrollConfig {
  enabled: boolean
  itemHeight: number
  buffer: number
  threshold: number
}

export const OptimizedCellRenderer = defineComponent({
  name: 'OptimizedCellRenderer',
  props: {
    items: {
      type: Array as PropType<RenderItem[]>,
      required: true,
    },
    virtualScroll: {
      type: Object as PropType<VirtualScrollConfig>,
      default: () => ({
        enabled: true,
        itemHeight: 40,
        buffer: 5,
        threshold: 50,
      }),
    },
    componentRegistry: {
      type: Map,
      required: true,
    },
  },
  setup(props) {
    const containerRef = ref<HTMLElement>()
    const scrollTop = ref(0)
    const containerHeight = ref(0)
    const isDestroying = ref(false)

    // 组件实例缓存
    const componentInstances = shallowRef(new Map())
    const renderBatch = ref(new Set<string>())

    // 计算可见范围
    const visibleRange = computed(() => {
      if (
        !props.virtualScroll.enabled ||
        props.items.length <= props.virtualScroll.threshold
      ) {
        return { start: 0, end: props.items.length }
      }

      const { itemHeight, buffer } = props.virtualScroll
      const start = Math.max(
        0,
        Math.floor(scrollTop.value / itemHeight) - buffer
      )
      const visibleCount = Math.ceil(containerHeight.value / itemHeight)
      const end = Math.min(
        props.items.length,
        start + visibleCount + buffer * 2
      )

      return { start, end }
    })

    // 可见项目
    const visibleItems = computed(() => {
      const { start, end } = visibleRange.value
      return props.items.slice(start, end).map((item, index) => ({
        ...item,
        index: start + index,
      }))
    })

    // 批量渲染处理
    const batchRender = () => {
      if (renderBatch.value.size === 0) return

      const batch = Array.from(renderBatch.value)
      renderBatch.value.clear()

      // 使用 requestIdleCallback 进行批量渲染
      if ('requestIdleCallback' in window) {
        requestIdleCallback(
          () => {
            batch.forEach((itemId) => {
              const item = props.items.find((i) => i.id === itemId)
              if (item && !item.rendered) {
                item.rendered = true
              }
            })
          },
          { timeout: 16 }
        ) // 最多等16ms
      } else {
        // 降级到 nextTick
        nextTick(() => {
          if (isDestroying.value) return
          batch.forEach((itemId) => {
            const item = props.items.find((i) => i.id === itemId)
            if (item && !item.rendered) {
              item.rendered = true
            }
          })
        })
      }
    }

    // 滚动处理
    const handleScroll = (event: Event) => {
      const target = event.target as HTMLElement
      scrollTop.value = target.scrollTop

      // 标记需要渲染的项目
      visibleItems.value.forEach((item) => {
        if (!item.rendered) {
          renderBatch.value.add(item.id)
        }
      })

      // 延迟批量渲染
      setTimeout(batchRender, 8)
    }

    // 获取组件实例（复用）
    const getComponentInstance = (componentName: string) => {
      if (componentInstances.value.has(componentName)) {
        return componentInstances.value.get(componentName)
      }

      const component = props.componentRegistry.get(componentName)
      if (component) {
        componentInstances.value.set(componentName, component)
        return component
      }

      return null
    }

    // 清理未使用的组件实例
    const cleanupInstances = () => {
      const usedComponents = new Set(
        visibleItems.value.map((item) => item.componentName)
      )

      componentInstances.value.forEach((instance, name) => {
        if (!usedComponents.has(name)) {
          componentInstances.value.delete(name)
        }
      })
    }

    // 生命周期
    onMounted(() => {
      if (containerRef.value) {
        containerHeight.value = containerRef.value.clientHeight

        // 监听容器大小变化
        const resizeObserver = new ResizeObserver((entries) => {
          for (const entry of entries) {
            containerHeight.value = entry.contentRect.height
          }
        })

        resizeObserver.observe(containerRef.value)

        // 清理
        onBeforeUnmount(() => {
          isDestroying.value = true
          resizeObserver.disconnect()
          componentInstances.value.clear()
        })
      }
    })

    // 定期清理
    const cleanupTimer = setInterval(cleanupInstances, 30000) // 30秒清理一次
    onBeforeUnmount(() => {
      isDestroying.value = true
      clearInterval(cleanupTimer)
    })

    return {
      containerRef,
      visibleItems,
      visibleRange,
      handleScroll,
      getComponentInstance,
    }
  },

  render() {
    const { items, virtualScroll } = this.$props
    const { visibleItems, visibleRange } = this

    // 非虚拟滚动模式
    if (!virtualScroll.enabled || items.length <= virtualScroll.threshold) {
      return (
        <div class="optimized-cell-renderer">
          {items.map((item: RenderItem) => (
            <div key={item.id} class="cell-item">
              {this.renderItem(item)}
            </div>
          ))}
        </div>
      )
    }

    // 虚拟滚动模式
    const { start, end } = visibleRange
    const totalHeight = items.length * virtualScroll.itemHeight
    const offsetY = start * virtualScroll.itemHeight

    return (
      <div
        ref={this.containerRef}
        class="optimized-cell-renderer virtual-scroll"
        onScroll={this.handleScroll}
        style={{ height: '100%', overflow: 'auto' }}
      >
        <div style={{ height: `${totalHeight}px`, position: 'relative' }}>
          <div
            style={{
              transform: `translateY(${offsetY}px)`,
              position: 'absolute',
              width: '100%',
            }}
          >
            {visibleItems.map((item: RenderItem & { index: number }) => (
              <div
                key={item.id}
                class="cell-item virtual-item"
                style={{ height: `${virtualScroll.itemHeight}px` }}
              >
                {this.renderItem(item)}
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  },

  methods: {
    renderItem(item: RenderItem) {
      const component = this.getComponentInstance(item.componentName)

      if (!component) {
        return <span class="fallback-text">{item.props.value || ''}</span>
      }

      // 懒加载渲染 - 只有可见且标记为需要渲染时才渲染
      if (this.virtualScroll.enabled && !item.visible && !item.rendered) {
        return (
          <div
            class="placeholder"
            style={{ height: `${this.virtualScroll.itemHeight}px` }}
          ></div>
        )
      }

      // 渲染实际组件 - 使用 h() 函数而不是 JSX
      return h(component, {
        ...item.props,
        key: `${item.id}-${item.componentName}`,
      })
    },
  },
})

// 导出辅助函数
export function createRenderItem(
  id: string,
  componentName: string,
  props: any,
  visible: boolean = true
): RenderItem {
  return {
    id,
    componentName,
    props,
    visible,
    rendered: false,
  }
}

export function createVirtualScrollConfig(
  enabled: boolean = true,
  itemHeight: number = 40,
  buffer: number = 5,
  threshold: number = 50
): VirtualScrollConfig {
  return {
    enabled,
    itemHeight,
    buffer,
    threshold,
  }
}
