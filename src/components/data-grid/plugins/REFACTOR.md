# 数据网格渲染器重构文档

## 重构概述

本次重构将数据网格渲染器系统进行了全面的模块化重构，将原本单文件的渲染器拆分为更清晰的模块结构，提升了代码的可维护性和复用性。

## 重构内容

### 1. 类型定义统一化

**新增文件：**

- `src/components/data-grid/plugins/types/index.ts` - 统一的类型定义文件

**主要接口：**

- `BaseRendererProps` - 基础渲染器属性接口
- `BaseRendererConfig` - 基础渲染器配置接口
- `ActionsRendererProps/Config` - 操作渲染器接口
- `BooleanRendererProps/Config` - 布尔值渲染器接口
- `LinkRendererProps/Config` - 链接渲染器接口
- `StatusRendererProps/Config` - 状态渲染器接口
- `CompositeRendererProps/Config` - 复合渲染器接口

### 2. 模块化渲染器结构

每个渲染器都被拆分为以下结构：

```
renderers/
├── actions/
│   ├── index.ts          # 模块导出文件
│   ├── renderer.vue      # Vue 组件
│   └── useRenderer.ts    # 逻辑钩子
├── boolean/
│   ├── index.ts
│   ├── renderer.vue
│   └── useRenderer.ts
├── link/
│   ├── index.ts
│   ├── renderer.vue
│   └── useRenderer.ts
├── status/
│   ├── index.ts
│   ├── renderer.vue
│   └── useRenderer.ts
├── composite/
│   ├── index.ts
│   ├── renderer.vue
│   └── useRenderer.ts
└── index.ts              # 总导出文件
```

### 3. 逻辑与视图分离

**优点：**

- **逻辑复用：** 逻辑钩子可以在不同组件间复用
- **测试友好：** 逻辑钩子可以独立测试
- **类型安全：** 统一的类型定义提升类型安全性
- **维护性：** 清晰的模块边界便于维护

**示例：**

```typescript
// 在 useRenderer.ts 中定义逻辑
export const useActionsRenderer = (props: ActionsRendererProps) => {
  // 所有业务逻辑
  return {
    // 暴露给组件的响应式数据和方法
  }
}

// 在 renderer.vue 中使用
const props = defineProps<ActionsRendererProps>()
const { directActions, handleActionClick } = useActionsRenderer(props)
```

### 4. 管理器更新

**更新内容：**

- `manager.ts` 引入新的类型定义
- Helper 方法使用强类型配置
- 向后兼容旧的接口

### 5. 文件变更对比

| 重构前                           | 重构后                                                                       |
| -------------------------------- | ---------------------------------------------------------------------------- |
| `ActionsRenderer.vue` (单文件)   | `actions/renderer.vue` + `actions/useRenderer.ts` + `actions/index.ts`       |
| `BooleanRenderer.vue` (单文件)   | `boolean/renderer.vue` + `boolean/useRenderer.ts` + `boolean/index.ts`       |
| `LinkRenderer.vue` (单文件)      | `link/renderer.vue` + `link/useRenderer.ts` + `link/index.ts`                |
| `StatusRenderer.vue` (单文件)    | `status/renderer.vue` + `status/useRenderer.ts` + `status/index.ts`          |
| `CompositeRenderer.vue` (单文件) | `composite/renderer.vue` + `composite/useRenderer.ts` + `composite/index.ts` |

## 使用方式

### 导入渲染器

```typescript
// 方式1：导入完整模块
import {
  ActionsRenderer,
  useActionsRenderer,
} from '@/components/data-grid/plugins/renderers/actions'

// 方式2：从总入口导入
import {
  ActionsRenderer,
  BooleanRenderer,
} from '@/components/data-grid/plugins/renderers'

// 方式3：导入类型
import type {
  ActionsRendererProps,
  ActionsRendererConfig,
} from '@/components/data-grid/plugins/types'
```

### 使用 Helper 创建列

```typescript
import { PluginManager } from '@/components/data-grid/plugins'

const manager = new PluginManager()
const helper = manager.getColumnHelper()

// 创建操作列（具备强类型支持）
const actionsColumn = helper.actions('操作', {
  actions: [
    {
      text: '编辑',
      icon: 'mdi:edit',
      type: 'primary',
      onClick: (row) => console.log('编辑', row),
    },
  ],
  layout: 'horizontal',
  showActionsCount: 2,
})
```

### 自定义渲染器

```typescript
// 创建自定义逻辑钩子
export const useCustomRenderer = (props: CustomRendererProps) => {
  // 自定义逻辑
  return {
    // 暴露的状态和方法
  }
}

// Vue 组件
<template>
  <!-- 模板 -->
</template>

<script setup lang="ts">
import type { CustomRendererProps } from '../types'
import { useCustomRenderer } from './useRenderer'

const props = defineProps<CustomRendererProps>()
const { /* 状态和方法 */ } = useCustomRenderer(props)
</script>
```

## 迁移指南

### 对于现有代码

1. **导入更新：** 更新渲染器组件的导入路径
2. **类型强化：** 使用新的类型定义提升类型安全
3. **逻辑复用：** 对于需要复用逻辑的场景，可以直接使用 `useRenderer` 钩子

### 向后兼容

- 管理器的 Helper 方法保持不变
- 组件接口保持兼容
- 原有的配置结构仍然有效

## 收益总结

1. **代码组织：** 清晰的模块结构，便于查找和维护
2. **逻辑复用：** 渲染器逻辑可以在不同场景复用
3. **类型安全：** 统一的类型定义，减少运行时错误
4. **测试友好：** 逻辑和视图分离，便于单元测试
5. **扩展性：** 新的渲染器可以轻松按照模板创建
6. **维护性：** 单一职责原则，每个文件职责明确

## 下一步计划

1. **文档完善：** 为每个渲染器添加详细的使用文档
2. **单元测试：** 为逻辑钩子添加完整的单元测试
3. **性能优化：** 优化渲染器的性能和内存使用
4. **新功能：** 基于新架构开发更多渲染器类型
