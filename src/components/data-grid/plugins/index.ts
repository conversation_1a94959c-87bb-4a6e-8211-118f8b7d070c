/**
 * 简化的插件系统入口
 * 替代复杂的插件系统，提供基本的渲染器功能
 */

import { PluginManager, type PluginRenderer } from './manager'

// 导入新的模块化渲染器组件
import { StatusRenderer } from './renderers/status'
import { <PERSON>oleanRenderer } from './renderers/boolean'
import { LinkRenderer } from './renderers/link'
import { ActionsRenderer } from './renderers/actions'
import { CompositeRenderer } from './renderers/composite'
import { DateRenderer } from './renderers/date'
import { NumberRenderer } from './renderers/number'
import { RelationRenderer } from './renderers/relation'
import { UserRenderer } from './renderers/user'
import { AuditInfoRenderer } from './renderers/audit/index'
import { StringRenderer } from './renderers/string'

// 定义核心渲染器
const coreRenderers: PluginRenderer[] = [
  {
    name: 'StatusRenderer',
    component: StatusRenderer,
    defaultWidth: 120,
  },
  {
    name: 'BooleanRender<PERSON>',
    component: <PERSON>olean<PERSON>enderer,
    defaultWidth: 80,
  },
  {
    name: '<PERSON><PERSON>enderer',
    component: LinkRenderer,
    defaultWidth: 150,
  },
  {
    name: 'ActionsRenderer',
    component: ActionsRenderer,
    defaultWidth: 120,
  },
  {
    name: 'CompositeRenderer',
    component: CompositeRenderer,
    defaultWidth: 200,
  },
  {
    name: 'DateRenderer',
    component: DateRenderer,
    defaultWidth: 150,
    defaultConfig: {
      format: 'yyyy-MM-dd',
      variant: 'text',
    },
  },
  {
    name: 'NumberRenderer',
    component: NumberRenderer,
    defaultWidth: 120,
    defaultConfig: {
      type: 'decimal',
      variant: 'text',
      nullText: '--',
    },
  },
  {
    name: 'RelationRenderer',
    component: RelationRenderer,
    defaultWidth: 150,
    defaultConfig: {
      variant: 'text',
      autoFromMetadata: true,
      overflowMode: 'tooltip',
      maxDisplay: 2,
    },
  },
  {
    name: 'UserRenderer',
    component: UserRenderer,
    defaultWidth: 120,
    defaultConfig: {
      variant: 'text',
      displayFormat: 'username',
      loadingText: '加载中...',
      errorText: '加载失败',
      nullText: '--',
      enableCache: true,
      showTooltip: false,
    },
  },
  {
    name: 'AuditInfoRenderer',
    component: AuditInfoRenderer,
    defaultWidth: 200,
    defaultConfig: {
      format: 'detailed',
      showCreated: true,
      showUpdated: true,
      dateFormat: {
        format: 'medium',
        showSeconds: true
      },
      userDisplay: {
        format: 'username',
        enableUserQuery: true,
        unknownUserText: '--'
      },
      theme: {
        fontSize: 'xs',
        spacing: 'compact',
        textColor: 'muted'
      }
    },
  },
  {
    name: 'StringRenderer',
    component: StringRenderer,
    defaultWidth: 150,
    defaultConfig: {
      variant: 'text',
      align: 'left',
      fontStyle: 'normal',
      theme: 'default',
      transform: 'none',
      copyable: false,
    },
  },
]

// 全局插件管理器实例
let globalManager: PluginManager | null = null

/**
 * 清理全局插件管理器 - 修复内存泄漏
 */
export function cleanupGlobalPluginManager(): void {
  if (globalManager) {
    globalManager.cleanup()
    globalManager = null
  }
}

/**
 * 创建简化的插件管理器
 */
export function createPluginManager(): PluginManager {
  const manager = new PluginManager()

  // 注册核心渲染器
  coreRenderers.forEach((renderer) => {
    manager.registerRenderer(renderer)
  })

  return manager
}

/**
 * 获取全局插件管理器
 */
export function getPluginManager(): PluginManager {
  if (!globalManager) {
    globalManager = createPluginManager()
  }
  return globalManager
}

// 导出类型和接口
export type { PluginRenderer } from './manager'
export { PluginManager, PluginHelper } from './manager'

// 页面卸载时清理全局管理器实例，防止内存泄漏
if (typeof window !== 'undefined') {
  const cleanup = () => {
    cleanupGlobalPluginManager()
  }

  // 监听页面卸载事件
  window.addEventListener('beforeunload', cleanup)
  window.addEventListener('pagehide', cleanup)

  // 在开发环境中，也监听热重载
  if (import.meta.env.DEV && import.meta.hot) {
    import.meta.hot.dispose(cleanup)
  }
}
