/**
 * 优化版性能监控配置
 * 减少监控开销，提高监控精度
 */

import { DEFAULT_PERFORMANCE_CONFIG } from '../config/performance'
import type { PerformanceConfig } from '../config/performance'

// 智能性能监控配置
export const OPTIMIZED_PERFORMANCE_CONFIG: PerformanceConfig = {
  ...DEFAULT_PERFORMANCE_CONFIG,
  monitoring: {
    enabled: import.meta.env.DEV,
    sampleInterval: 5000, // 从2秒改为5秒，减少开销
    warningThreshold: 500, // 降低警告阈值，更早发现问题
    logToConsole: false, // 默认关闭控制台日志，减少输出开销
  },
  memory: {
    maxCacheEntries: 100, // 增加缓存条目
    cacheExpiration: 10 * 60 * 1000, // 10分钟过期
    autoCleanup: true,
  },
  render: {
    enableColumnCache: true,
    enableComponentCache: true,
    debounceDelay: 150, // 减少防抖延迟，提高响应性
  },
}

/**
 * 轻量级性能监控器
 * 专注于关键指标，减少监控开销
 */
export class LightweightPerformanceMonitor {
  private metrics = new Map<
    string,
    {
      renderCount: number
      totalRenderTime: number
      lastRenderTime: number
      avgRenderTime: number
      cacheHits: number
      cacheMisses: number
    }
  >()

  private enabled = import.meta.env.DEV
  private sampleRate = 0.1 // 只监控10%的渲染

  constructor(sampleRate: number = 0.1) {
    this.sampleRate = sampleRate
  }

  // 开始渲染监控（采样）
  startRender(componentId: string): string | null {
    if (!this.enabled || Math.random() > this.sampleRate) {
      return null
    }

    const markId = `${componentId}-${Date.now()}`
    performance.mark(`${markId}-start`)
    return markId
  }

  // 结束渲染监控
  endRender(markId: string | null, componentId: string): void {
    if (!this.enabled || !markId) return

    try {
      performance.mark(`${markId}-end`)
      performance.measure(
        `${markId}-duration`,
        `${markId}-start`,
        `${markId}-end`
      )

      const measure = performance.getEntriesByName(`${markId}-duration`)[0]
      if (measure) {
        this.recordRenderMetric(componentId, measure.duration)
      }

      // 清理 performance entries
      performance.clearMarks(`${markId}-start`)
      performance.clearMarks(`${markId}-end`)
      performance.clearMeasures(`${markId}-duration`)
    } catch (error) {
      // 忽略性能监控错误，不影响主流程
    }
  }

  // 记录渲染指标
  private recordRenderMetric(componentId: string, duration: number): void {
    const current = this.metrics.get(componentId) || {
      renderCount: 0,
      totalRenderTime: 0,
      lastRenderTime: 0,
      avgRenderTime: 0,
      cacheHits: 0,
      cacheMisses: 0,
    }

    current.renderCount++
    current.totalRenderTime += duration
    current.lastRenderTime = duration
    current.avgRenderTime = current.totalRenderTime / current.renderCount

    this.metrics.set(componentId, current)

    // 如果渲染时间过长，输出警告
    if (duration > OPTIMIZED_PERFORMANCE_CONFIG.monitoring.warningThreshold) {
      console.warn(
        `⚠️ [Performance] ${componentId} 渲染耗时 ${duration.toFixed(2)}ms`
      )
    }
  }

  // 记录缓存命中
  recordCacheHit(componentId: string, hit: boolean): void {
    if (!this.enabled) return

    const current = this.metrics.get(componentId)
    if (current) {
      if (hit) {
        current.cacheHits++
      } else {
        current.cacheMisses++
      }
    }
  }

  // 获取性能报告
  getReport(): string {
    const report: string[] = ['📊 轻量级性能监控报告:']

    this.metrics.forEach((metric, componentId) => {
      const cacheHitRate =
        metric.cacheHits + metric.cacheMisses > 0
          ? (
              (metric.cacheHits / (metric.cacheHits + metric.cacheMisses)) *
              100
            ).toFixed(1)
          : '0'

      report.push(
        `  ${componentId}: 平均渲染${metric.avgRenderTime.toFixed(2)}ms, ` +
          `缓存命中率${cacheHitRate}%, 渲染次数${metric.renderCount}`
      )
    })

    return report.join('\n')
  }

  // 清理指标
  cleanup(): void {
    this.metrics.clear()
  }

  // 获取所有指标（供优化器使用）
  getAllMetrics() {
    return new Map(this.metrics)
  }
}

// 全局轻量级监控实例
export const lightweightMonitor = new LightweightPerformanceMonitor()

// 在开发环境下暴露到全局
if (import.meta.env.DEV && typeof window !== 'undefined') {
  ;(window as any).__LIGHTWEIGHT_PERF__ = {
    monitor: lightweightMonitor,
    report: () => console.log(lightweightMonitor.getReport()),
    cleanup: () => lightweightMonitor.cleanup(),
  }
}

/**
 * 性能优化建议器
 */
export class PerformanceOptimizer {
  private monitor: LightweightPerformanceMonitor

  constructor(monitor: LightweightPerformanceMonitor) {
    this.monitor = monitor
  }

  // 分析性能并给出建议
  analyzePerformance(): {
    issues: string[]
    suggestions: string[]
  } {
    const issues: string[] = []
    const suggestions: string[] = []

    this.monitor.getAllMetrics().forEach((metric, componentId) => {
      // 检查渲染时间
      if (metric.avgRenderTime > 100) {
        issues.push(
          `${componentId} 平均渲染时间过长 (${metric.avgRenderTime.toFixed(2)}ms)`
        )
        suggestions.push(
          `优化 ${componentId} 组件，考虑使用 v-memo 或减少计算属性`
        )
      }

      // 检查缓存命中率
      const totalCache = metric.cacheHits + metric.cacheMisses
      if (totalCache > 10) {
        const hitRate = metric.cacheHits / totalCache
        if (hitRate < 0.5) {
          issues.push(
            `${componentId} 缓存命中率过低 (${(hitRate * 100).toFixed(1)}%)`
          )
          suggestions.push(`优化 ${componentId} 的缓存策略，检查缓存键是否合理`)
        }
      }

      // 检查渲染频率
      if (metric.renderCount > 100) {
        issues.push(`${componentId} 渲染次数过多 (${metric.renderCount} 次)`)
        suggestions.push(`检查 ${componentId} 是否有不必要的重新渲染`)
      }
    })

    return { issues, suggestions }
  }

  // 自动优化建议
  getOptimizationTips(): string[] {
    const { issues, suggestions } = this.analyzePerformance()
    const tips: string[] = []

    if (issues.length === 0) {
      tips.push('✅ 目前性能表现良好！')
    } else {
      tips.push('🔧 发现的性能问题:')
      tips.push(...issues.map((issue) => `  ❌ ${issue}`))
      tips.push('')
      tips.push('💡 优化建议:')
      tips.push(...suggestions.map((suggestion) => `  🚀 ${suggestion}`))
    }

    return tips
  }
}

// 全局优化建议器
export const performanceOptimizer = new PerformanceOptimizer(lightweightMonitor)
