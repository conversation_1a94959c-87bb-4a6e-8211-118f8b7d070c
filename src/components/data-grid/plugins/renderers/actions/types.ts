import {
  BaseRendererConfig,
  BaseRendererProps,
  ButtonType,
  ButtonVariant,
} from '../../types'

/**
 * 操作配置接口
 */
export interface ActionConfig {
  /** 显示文本 */
  text?: string
  /** 图标 */
  icon?: string
  /** 按钮类型 */
  type?: ButtonType
  /** 按钮变体 */
  variant?: ButtonVariant
  /** 按钮大小 */
  size?: 'small' | 'medium' | 'large' | 'default' | 'sm' | 'lg' | 'icon'
  /** 工具提示 */
  tooltip?: string
  /** 点击事件处理器 */
  onClick: (row: any) => void
  /** 显示条件 */
  condition?: (row: any) => boolean
  /** 禁用条件 */
  disabled?: (row: any) => boolean
  /** 确认消息 */
  confirm?: string | ((row: any) => string)
}

/**
 * 操作渲染器配置接口
 */
export interface ActionsRendererConfig extends BaseRendererConfig {
  /** 操作列表 */
  actions?: ActionConfig[]
  /** 布局方式 */
  layout?: 'horizontal' | 'dropdown'
  /** 直接显示的操作按钮数量 */
  showActionsCount?: number
}

/**
 * 操作渲染器属性接口
 */
export interface ActionsRendererProps extends BaseRendererProps {
  config?: ActionsRendererConfig
}
