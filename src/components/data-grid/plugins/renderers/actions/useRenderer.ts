/**
 * 操作渲染器逻辑钩子
 */

import { computed } from 'vue'
import type { ActionsRendererProps, ActionConfig } from './types'
import {
  getButtonVariant as utilGetButtonVariant,
  getDropdownItemClasses as utilGetDropdownItemClasses,
  mapButtonSize,
} from '../../utils'

export const useActionsRenderer = (props: ActionsRendererProps) => {
  // 操作列表
  const actions = computed(() => props.config?.actions || [])

  // 过滤可见的操作
  const visibleActions = computed(() => {
    return actions.value.filter((action) => {
      if (action.condition) {
        return action.condition(props.row)
      }
      return true
    })
  })

  // 获取直接显示的操作按钮
  const directActions = computed(() => {
    const showCount = props.config?.showActionsCount || 2
    const layout = props.config?.layout || 'horizontal'

    if (layout === 'dropdown') {
      return []
    }

    return visibleActions.value.slice(0, showCount)
  })

  // 获取下拉菜单中的操作按钮
  const dropdownActions = computed(() => {
    const showCount = props.config?.showActionsCount || 2
    const layout = props.config?.layout || 'horizontal'

    if (layout === 'dropdown') {
      return visibleActions.value
    }

    return visibleActions.value.slice(showCount)
  })

  // 是否有更多操作
  const hasMoreActions = computed(() => {
    return dropdownActions.value.length > 0
  })

  // 获取按钮变体
  const getButtonVariant = (action: ActionConfig) => {
    return utilGetButtonVariant(action.type, action.variant)
  }

  // 获取下拉菜单项样式类
  const getDropdownItemClasses = (action: ActionConfig) => {
    const variant = getButtonVariant(action)
    return utilGetDropdownItemClasses(variant)
  }

  // 获取按钮大小
  const getButtonSize = (action: ActionConfig) => {
    return mapButtonSize(action.size)
  }

  // 检查操作是否禁用
  const isActionDisabled = (action: ActionConfig) => {
    if (action.disabled && typeof action.disabled === 'function') {
      return action.disabled(props.row)
    }
    return false
  }

  // 处理操作点击
  const handleActionClick = async (action: ActionConfig) => {
    try {
      // 检查是否禁用
      if (isActionDisabled(action)) {
        return
      }

      // 显示确认对话框
      if (action.confirm) {
        const confirmMessage =
          typeof action.confirm === 'function'
            ? action.confirm(props.row)
            : action.confirm

        if (!window.confirm(confirmMessage)) {
          return
        }
      }

      // 执行操作
      if (action.onClick && typeof action.onClick === 'function') {
        action.onClick(props.row)
      }
    } catch (error) {
      console.error('Action execution failed:', error)
    }
  }

  return {
    actions,
    visibleActions,
    directActions,
    dropdownActions,
    hasMoreActions,
    getButtonVariant,
    getDropdownItemClasses,
    getButtonSize,
    isActionDisabled,
    handleActionClick,
  }
}
