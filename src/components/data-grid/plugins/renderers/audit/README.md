# 审计信息渲染器 (AuditInfoRenderer)

提供审计信息的智能显示，支持创建/更新信息的多种展示格式。

## 功能特性

- **多种显示格式**：`compact`（紧凑）、`detailed`（详细）、`smart`（智能）
- **用户名查询**：支持用户ID转用户名，带缓存机制
- **智能日期格式化**：支持相对时间、绝对时间等多种格式
- **响应式状态管理**：加载状态、错误处理、内存泄漏防护
- **可配置样式**：支持字体大小、间距、颜色等自定义

## 使用方法

### 基本用法

```vue
<template>
  <AuditInfoRenderer 
    :row="rowData" 
    :config="config"
  />
</template>

<script setup>
import { AuditInfoRenderer } from '@/components/data-grid/plugins/renderers/audit'

const config = {
  format: 'smart',
  showCreated: true,
  showUpdated: true
}
</script>
```

### 配置选项

```typescript
interface AuditInfoRendererConfig {
  /** 显示格式 */
  format?: 'compact' | 'detailed' | 'smart'
  
  /** 是否显示创建信息 */
  showCreated?: boolean
  
  /** 是否显示更新信息 */
  showUpdated?: boolean
  
  /** 日期格式化选项 */
  dateFormat?: {
    format?: 'short' | 'medium' | 'long' | 'full' | 'smart' | 'relative' | 'auto'
    showRelative?: boolean
    showSeconds?: boolean
    relativeThreshold?: number
  }
  
  /** 用户信息渲染配置 */
  userDisplay?: {
    format?: 'id' | 'username' | 'name' | 'full'
    enableUserQuery?: boolean
    unknownUserText?: string
  }
  
  /** 样式主题 */
  theme?: {
    fontSize?: 'xs' | 'sm' | 'base'
    spacing?: 'compact' | 'comfortable'
    textColor?: 'muted' | 'secondary' | 'primary'
  }
}
```

## 数据结构要求

组件期望行数据包含以下字段：

```typescript
interface RowData {
  created_by?: number | string | null    // 创建人ID
  updated_by?: number | string | null    // 更新人ID
  created_at?: string | null             // 创建时间
  updated_at?: string | null             // 更新时间
}
```

## 显示格式

### Smart 格式（推荐）
- 自动显示创建信息
- 当有更新时显示更新信息
- 鼠标悬停显示详细信息
- 使用相对时间显示（如"2天前"）

### Detailed 格式
- 分别显示创建和更新信息
- 每行显示完整的用户名和时间
- 适合需要详细信息的场景

### Compact 格式
- 优先显示更新信息，无更新时显示创建信息
- 单行显示，节省空间
- 适合表格密集显示

## 性能特性

- **内存管理**：组件卸载时自动清理资源
- **用户缓存**：用户名查询结果缓存，减少API调用
- **异步加载**：用户名查询异步进行，不阻塞渲染
- **错误处理**：网络错误时优雅降级显示

## 与数据网格集成

```typescript
// 在 DataGrid 中使用
const columnConfig = {
  field: 'audit_info',
  title: '审计信息',
  renderer: 'AuditInfoRenderer',
  width: 200,
  rendererConfig: {
    format: 'smart',
    userDisplay: {
      enableUserQuery: true
    }
  }
}
```