<template>
  <div :class="containerClasses">
    <!-- 智能格式 - 创建信息始终显示，更新信息有数据时显示 -->
    <div v-if="finalConfig.format === 'smart'" :class="itemClasses">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger as-child>
            <div class="cursor-help">
              <!-- 创建信息 - 始终显示 -->
              <div class="flex items-center gap-1">
                <span class="text-xs text-muted-foreground">创建</span>
                <span class="font-medium text-green-600">{{ formatUser(auditInfo.createdBy) }}</span>
                <span>{{ formatDateOnly(auditInfo.createdAt) }}</span>
              </div>
              
              <!-- 更新信息 - 仅在有更新数据时显示 -->
              <div v-if="hasUpdateInfo" class="flex items-center gap-1 mt-0.5">
                <span class="text-xs text-muted-foreground">更新</span>
                <span class="font-medium text-blue-600">{{ formatUser(auditInfo.updatedBy) }}</span>
                <span>{{ formatDateOnly(auditInfo.updatedAt) }}</span>
              </div>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div class="space-y-1">
              <!-- 详细创建信息 -->
              <div class="flex items-center gap-2">
                <span class="text-green-600 font-medium">创建:</span>
                <span>{{ formatUser(auditInfo.createdBy) }}</span>
                <span>{{ formatDateTime(auditInfo.createdAt, 'full') }}</span>
              </div>
              <!-- 详细更新信息 -->
              <div v-if="hasUpdateInfo" class="flex items-center gap-2">
                <span class="text-blue-600 font-medium">更新:</span>
                <span>{{ formatUser(auditInfo.updatedBy) }}</span>
                <span>{{ formatDateTime(auditInfo.updatedAt, 'full') }}</span>
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>

    <!-- 详细格式 -->
    <template v-else-if="finalConfig.format === 'detailed'">
      <div v-if="finalConfig.showCreated && auditInfo.createdAt" :class="itemClasses">
        <span class="font-medium">创建：</span>
        <span v-if="auditInfo.createdBy && finalConfig.showCreated">{{ formatUser(auditInfo.createdBy) }} • </span>
        <span>{{ formatDateTime(auditInfo.createdAt) }}</span>
      </div>
      
      <div v-if="finalConfig.showUpdated && hasUpdateInfo" :class="itemClasses">
        <span class="font-medium">更新：</span>
        <span v-if="auditInfo.updatedBy">{{ formatUser(auditInfo.updatedBy) }} • </span>
        <span>{{ formatDateTime(auditInfo.updatedAt) }}</span>
      </div>
    </template>
    
    <!-- 紧凑格式 -->
    <div v-else-if="finalConfig.format === 'compact' && (auditInfo.createdAt || auditInfo.updatedAt)" :class="itemClasses">
      <template v-if="hasUpdateInfo">
        <span class="font-medium">更新：</span>
        <span v-if="auditInfo.updatedBy">{{ formatUser(auditInfo.updatedBy) }} • </span>
        <span>{{ formatDateTime(auditInfo.updatedAt) }}</span>
      </template>
      <template v-else>
        <span class="font-medium">创建：</span>
        <span v-if="auditInfo.createdBy">{{ formatUser(auditInfo.createdBy) }} • </span>
        <span>{{ formatDateTime(auditInfo.createdAt) }}</span>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'AuditInfoRenderer',
})

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import type { AuditInfoRendererProps } from './types'
import { useAuditInfoRenderer } from './useRenderer'

const props = defineProps<AuditInfoRendererProps>()

const {
  auditInfo,
  hasUpdateInfo,
  finalConfig,
  formatUser,
  formatDateTime,
  formatDateOnly,
  containerClasses,
  itemClasses,
} = useAuditInfoRenderer(props)
</script>

<style scoped>
.audit-info {
  min-width: 200px;
  line-height: 1.3;
}

/* 紧凑布局样式 */
.audit-info.compact {
  min-width: 160px;
}

/* 间距样式 */
.audit-info.comfortable {
  line-height: 1.5;
}
</style>