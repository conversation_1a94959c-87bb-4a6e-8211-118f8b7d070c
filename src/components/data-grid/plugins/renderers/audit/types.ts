/**
 * 审计信息渲染器类型定义
 * 
 * 用于定义审计信息渲染相关的配置和接口
 * 支持多种显示格式和样式配置
 */

import type { BaseRendererConfig, BaseRendererProps } from '../../types'

export interface AuditInfoRendererConfig extends BaseRendererConfig {
  /** 显示格式 */
  format?: 'compact' | 'detailed' | 'smart'
  
  /** 是否显示创建信息 */
  showCreated?: boolean
  
  /** 是否显示更新信息 */
  showUpdated?: boolean
  
  /** 日期格式化选项 */
  dateFormat?: {
    /** 日期时间格式 */
    format?: 'short' | 'medium' | 'long' | 'full' | 'smart' | 'relative' | 'auto'
    /** 是否显示相对时间 */
    showRelative?: boolean
    /** 是否显示秒数 */
    showSeconds?: boolean
    /** 相对时间阈值（天） */
    relativeThreshold?: number
  }
  
  /** 用户信息渲染配置 */
  userDisplay?: {
    /** 用户显示格式 */
    format?: 'id' | 'username' | 'name' | 'full'
    /** 是否启用用户查询 */
    enableUserQuery?: boolean
    /** 未知用户显示文本 */
    unknownUserText?: string
  }
  
  /** 样式主题 */
  theme?: {
    /** 文字大小 */
    fontSize?: 'xs' | 'sm' | 'base'
    /** 行间距 */
    spacing?: 'compact' | 'comfortable'
    /** 文字颜色 */
    textColor?: 'muted' | 'secondary' | 'primary'
  }
}

export interface AuditInfoRendererProps extends BaseRendererProps {
  config?: AuditInfoRendererConfig
}

/** 审计信息状态类型 */
export interface AuditInfo {
  createdBy?: number | string | null
  updatedBy?: number | string | null
  createdAt?: string | null
  updatedAt?: string | null
}

/** 用户信息加载状态 */
export type UserLoadingState = 
  | { type: 'idle' }
  | { type: 'loading' }
  | { type: 'success'; createdByName?: string | null; updatedByName?: string | null }
  | { type: 'error'; message?: string }