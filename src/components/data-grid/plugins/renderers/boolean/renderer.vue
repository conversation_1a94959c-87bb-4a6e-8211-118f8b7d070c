<template>
  <div class="inline-flex items-center">
    <!-- Badge 变体 -->
    <Badge v-if="variant === 'badge'" :class="badgeClasses">
      <Icon
        v-if="currentConfig.icon"
        :icon="currentConfig.icon"
        class="mr-1 inline-block"
      />
      {{ currentConfig.text }}
    </Badge>

    <!-- Switch 变体 -->
    <Switch
      v-else-if="variant === 'switch'"
      :checked="booleanValue"
      :disabled="true"
      class="pointer-events-none"
    />

    <!-- Icon 变体 -->
    <Icon
      v-else-if="variant === 'icon' && currentConfig.icon"
      :icon="currentConfig.icon"
      :class="iconClasses"
    />

    <!-- Text 变体 -->
    <span v-else :class="textClasses">
      {{ currentConfig.text }}
    </span>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'BooleanRender<PERSON>',
})

import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Icon } from '@iconify/vue'
import type { BooleanRendererProps } from './types'
import { useBooleanRenderer } from './useRenderer'

const props = defineProps<BooleanRendererProps>()

const {
  booleanValue,
  variant,
  currentConfig,
  badgeClasses,
  iconClasses,
  textClasses,
} = useBooleanRenderer(props)
</script>
