import { BaseRendererConfig, BaseRendererProps } from '../../types'

/**
 * 布尔值渲染器配置接口
 */
export interface BooleanRendererConfig extends BaseRendererConfig {
  /** 变体类型 */
  variant?: 'switch' | 'badge' | 'icon' | 'text'
  /** 真值文本 */
  trueText?: string
  /** 假值文本 */
  falseText?: string
  /** 真值配置 */
  trueConfig?: { text: string; type?: string; icon?: string }
  /** 假值配置 */
  falseConfig?: { text: string; type?: string; icon?: string }
}

/**
 * 布尔值渲染器属性接口
 */
export interface BooleanRendererProps extends BaseRendererProps {
  config?: BooleanRendererConfig
}
