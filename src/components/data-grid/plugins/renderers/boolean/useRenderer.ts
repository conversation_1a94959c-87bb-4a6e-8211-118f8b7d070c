/**
 * 布尔值渲染器逻辑钩子
 */

import { computed } from 'vue'
import { cn } from '@/lib/utils'
import { createThemeClassBuilder } from '../../utils/rendererUtils'
import type { BooleanRendererProps } from './types'

export const useBooleanRenderer = (props: BooleanRendererProps) => {
  // 获取布尔值 - 保持原有逻辑
  const value = computed(() => {
    return props.field ? props.row?.[props.field] : props.value
  })

  const booleanValue = computed(() => {
    const val = value.value
    if (typeof val === 'boolean') return val
    if (typeof val === 'number') return val === 1
    if (typeof val === 'string')
      return val === 'true' || val === '1' || val === 'yes'
    return false
  })

  const variant = computed(() => props.config?.variant || 'badge')

  // 获取当前配置 - 简化逻辑
  const currentConfig = computed(() => {
    const isTrue = booleanValue.value

    if (isTrue) {
      return (
        props.config?.trueConfig || {
          text: props.config?.trueText || '是',
          type: 'success',
          icon: 'check-circle',
        }
      )
    } else {
      return (
        props.config?.falseConfig || {
          text: props.config?.falseText || '否',
          type: 'danger',
          icon: 'x-circle',
        }
      )
    }
  })

  // 使用统一的样式构建器 - 遵循DRY原则
  const styleBuilder = computed(() => {
    const type = currentConfig.value.type || 'default'
    return createThemeClassBuilder(type)
  })

  // 简化的样式类 - 移除重复代码
  const badgeClasses = computed(() => {
    return styleBuilder.value.badge()
  })

  const iconClasses = computed(() => {
    return cn('w-4 h-4', styleBuilder.value.text())
  })

  const textClasses = computed(() => {
    return styleBuilder.value.text()
  })

  return {
    value,
    booleanValue,
    variant,
    currentConfig,
    badgeClasses,
    iconClasses,
    textClasses,
  }
}
