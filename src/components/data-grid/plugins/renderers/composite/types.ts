import {
  BaseRendererConfig,
  BaseRendererProps,
  ButtonType,
  ButtonVariant,
} from '../../types'

export interface mainContentConfig {
  field: string
  formatter?: (value: any, row: any) => string
  className?: string
  style?: Record<string, any>
}

export interface subsContentConfig {
  items?: Array<{
    field: string
    label?: string
    formatter?: (value: any, row: any) => string
    condition?: (row: any) => boolean
    className?: string
  }>
  layout?: 'horizontal' | 'vertical'
  separator?: string
}

export interface iconConfig {
  type?: 'icon' | 'image' | 'avatar'
  icon?: string
  imageField?: string
  avatarField?: string
  nameField?: string
  size?: number
}

export interface actionButtonConfig {
  key?: string
  text?: string
  icon: string
  tooltip?: string
  type?: ButtonType
  variant?: ButtonVariant
  size?: 'icon' | 'default' | 'xs' | 'sm' | 'lg'
  onClick: (row: any) => void
  condition?: (row: any) => boolean
}

/**
 * 复合渲染器配置接口
 */
export interface CompositeRendererConfig extends BaseRendererConfig {
  /** 主内容配置 */
  main: mainContentConfig
  /** 子内容配置 */
  subs?: subsContentConfig
  /** 图标配置 */
  icon?: iconConfig
  /** 操作配置 */
  actions?: Array<actionButtonConfig>
  /** 显示的操作按钮数量 */
  showActionsCount?: number
  /** 是否启用悬停效果 */
  enableHover?: boolean
}

/**
 * 复合渲染器属性接口
 */
export interface CompositeRendererProps extends BaseRendererProps {
  config?: CompositeRendererConfig
}
