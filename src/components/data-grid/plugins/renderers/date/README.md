# DateRenderer 日期渲染器使用说明

## 功能特点

DateRenderer 是一个功能丰富的日期时间渲染器，支持多种显示格式和交互方式。

### 主要特性

1. **多种日期格式**

   - `yyyy-MM-dd` - 标准日期格式
   - `MM/dd/yyyy` - 美式日期格式
   - `dd/MM/yyyy` - 欧式日期格式
   - `yyyy年MM月dd日` - 中文日期格式
   - `relative` - 相对时间（2小时前、昨天等）
   - `smart` - 智能格式（今天显示时间，其他显示日期）
   - `full-datetime` - 完整日期时间
   - 自定义格式函数

2. **多种显示变体**

   - `text` - 纯文本显示
   - `badge` - 徽章样式
   - `tooltip` - 带工具提示
   - `relative-tooltip` - 相对时间 + 工具提示

3. **时区支持**

   - 支持任意时区显示
   - 可配置时区信息显示格式

4. **主题定制**
   - 多种颜色主题
   - 字体大小和粗细调整
   - 自定义CSS类

## 基本用法

```typescript
import { createPluginManager } from '@/components/data-grid/plugins'

const manager = createPluginManager()
const columnHelper = manager.getColumnHelper()

// 基础日期列
const dateColumn = columnHelper.date('createdAt', '创建时间')

// 日期时间列
const datetimeColumn = columnHelper.datetime('updatedAt', '更新时间')

// 相对时间列
const relativeColumn = columnHelper.relativeTime('lastLogin', '最后登录')

// 智能时间列
const smartColumn = columnHelper.smartTime('publishTime', '发布时间')

// 时间戳徽章列
const timestampColumn = columnHelper.timestamp('eventTime', '事件时间')
```

## 高级配置

### 自定义日期格式

```typescript
const customDateColumn = columnHelper.date('date', '自定义日期', {
  format: (date: Date) => {
    return `${date.getFullYear()}年第${Math.ceil((date.getMonth() + 1) / 3)}季度`
  },
  variant: 'badge',
  theme: {
    badgeColor: 'info',
  },
})
```

### 时区配置

```typescript
const timezoneColumn = columnHelper.datetime('utcTime', 'UTC时间', {
  timezone: {
    timezone: 'UTC',
    showTimezone: true,
    timezoneFormat: 'offset',
  },
  tooltip: {
    enabled: true,
    extraInfo: {
      showTimezone: true,
    },
  },
})
```

### 相对时间配置

```typescript
const relativeColumn = columnHelper.relativeTime('createTime', '创建时间', {
  relative: {
    maxDays: 7,
    labels: {
      justNow: '刚刚',
      minutesAgo: '{n}分钟前',
      hoursAgo: '{n}小时前',
      yesterday: '昨天',
      daysAgo: '{n}天前',
    },
  },
})
```

### 工具提示配置

```typescript
const tooltipColumn = columnHelper.date('deadline', '截止时间', {
  variant: 'tooltip',
  tooltip: {
    enabled: true,
    format: 'full-datetime',
    extraInfo: {
      showWeekday: true,
      showRelative: true,
      customInfo: (date: Date) => {
        const now = new Date()
        const diff = date.getTime() - now.getTime()
        const days = Math.ceil(diff / (1000 * 60 * 60 * 24))
        return days > 0 ? [`还有${days}天`] : ['已过期']
      },
    },
  },
  theme: {
    textColor: 'warning',
  },
})
```

### 主题定制

```typescript
const themedColumn = columnHelper.date('eventDate', '事件日期', {
  variant: 'badge',
  theme: {
    badgeColor: 'success',
    fontSize: 'sm',
    fontWeight: 'medium',
    className: 'custom-date-badge',
  },
})
```

### 验证配置

```typescript
const validatedColumn = columnHelper.date('birthDate', '出生日期', {
  validation: {
    minDate: new Date('1900-01-01'),
    maxDate: new Date(),
    invalidText: '无效日期',
    emptyText: '未填写',
  },
  theme: {
    textColor: 'muted',
  },
})
```

### 事件处理

```typescript
const interactiveColumn = columnHelper.date('appointmentTime', '预约时间', {
  onClick: (date: Date, row: any) => {
    console.log('点击日期:', date, '行数据:', row)
    // 可以打开日历、显示详情等
  },
  onDoubleClick: (date: Date, row: any) => {
    console.log('双击编辑日期:', date)
    // 可以打开编辑器
  },
})
```

## 完整示例

```typescript
// 构建一个包含多种日期列的数据网格
const columns = [
  // 基础信息
  columnHelper.createColumn('id', 'ID'),
  columnHelper.createColumn('name', '名称'),

  // 各种日期时间列
  columnHelper.date('createdAt', '创建时间', {
    format: 'yyyy-MM-dd',
    variant: 'text',
  }),

  columnHelper.relativeTime('lastModified', '最后修改', {
    relative: {
      maxDays: 30,
    },
  }),

  columnHelper.smartTime('publishTime', '发布时间', {
    timezone: {
      timezone: 'Asia/Shanghai',
    },
  }),

  columnHelper.timestamp('deadline', '截止时间', {
    theme: {
      badgeColor: 'warning',
    },
    validation: {
      minDate: new Date(),
    },
  }),

  // 自定义格式
  columnHelper.date('quarter', '季度', {
    format: (date: Date) => {
      const quarter = Math.ceil((date.getMonth() + 1) / 3)
      return `${date.getFullYear()}Q${quarter}`
    },
    variant: 'badge',
    theme: {
      badgeColor: 'info',
    },
  }),
]
```

## 支持的时区

DateRenderer 支持所有标准的 IANA 时区标识符，常用的包括：

- `UTC` - 协调世界时
- `Asia/Shanghai` - 中国标准时间
- `America/New_York` - 美国东部时间
- `Europe/London` - 英国时间
- `Asia/Tokyo` - 日本标准时间

## 注意事项

1. **性能优化**：相对时间计算会在每次渲染时执行，建议在大量数据时谨慎使用
2. **时区处理**：确保数据源的时区一致性，避免时区混乱
3. **格式化函数**：自定义格式化函数应该是纯函数，避免副作用
4. **响应式设计**：在移动端使用时注意日期显示的长度和布局

## 扩展建议

如需更多功能，可以考虑：

1. 添加日期范围选择器集成
2. 支持更多本地化语言
3. 添加日历弹出层
4. 集成节假日显示
5. 支持自定义日期解析器
