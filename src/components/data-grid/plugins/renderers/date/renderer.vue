<template>
  <div :class="cssClasses" @click="handleClick" @dblclick="handleDoubleClick">
    <!-- 文本变体 -->
    <span v-if="variant === 'text'" :title="tooltipText" class="date-text">
      {{ displayText }}
    </span>

    <!-- 徽章变体 -->
    <span
      v-else-if="variant === 'badge'"
      :title="tooltipText"
      class="date-badge inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
      :class="badgeClasses"
    >
      <Icon v-if="showIcon" :icon="iconName" class="w-3 h-3 mr-1" />
      {{ displayText }}
    </span>

    <!-- 工具提示变体 -->
    <TooltipProvider
      v-else-if="variant === 'tooltip' || variant === 'relative-tooltip'"
    >
      <Tooltip>
        <TooltipTrigger as-child>
          <span
            class="date-tooltip cursor-help border-b border-dotted border-gray-400"
            :class="textClasses"
          >
            <Icon
              v-if="showIcon"
              :icon="iconName"
              class="w-4 h-4 mr-1 inline"
            />
            {{ displayText }}
          </span>
        </TooltipTrigger>
        <TooltipContent v-if="tooltipText">
          <p class="whitespace-pre-line">{{ tooltipText }}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>

    <!-- 默认文本（兜底） -->
    <span v-else :title="tooltipText" class="date-default" :class="textClasses">
      {{ displayText }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import type { DateRendererProps } from './types'
import { useDateRenderer } from './useRenderer'

// 定义组件选项
defineOptions({
  name: 'DateRenderer',
})

// 定义属性
const props = defineProps<DateRendererProps>()

// 使用渲染器组合函数
const {
  displayText,
  tooltipText,
  variant,
  theme,
  cssClasses,
  formattedResult,
  handleClick,
  handleDoubleClick,
} = useDateRenderer(props)

// 计算图标相关
const showIcon = computed(() => {
  return (
    variant.value === 'badge' ||
    variant.value === 'tooltip' ||
    variant.value === 'relative-tooltip'
  )
})

const iconName = computed(() => {
  if (!formattedResult.value.isValid) {
    return 'mdi:alert-circle'
  }

  if (formattedResult.value.isRelative) {
    return 'mdi:clock-outline'
  }

  return 'mdi:calendar'
})

// 徽章样式类
const badgeClasses = computed(() => {
  const classes: string[] = []
  const badgeColor = theme.value.badgeColor || 'default'

  if (!formattedResult.value.isValid) {
    classes.push('bg-red-100 text-red-800')
  } else if (formattedResult.value.isRelative) {
    classes.push('bg-blue-100 text-blue-800')
  } else {
    switch (badgeColor) {
      case 'primary':
        classes.push('bg-blue-100 text-blue-800')
        break
      case 'secondary':
        classes.push('bg-gray-100 text-gray-800')
        break
      case 'success':
        classes.push('bg-green-100 text-green-800')
        break
      case 'warning':
        classes.push('bg-yellow-100 text-yellow-800')
        break
      case 'danger':
        classes.push('bg-red-100 text-red-800')
        break
      case 'info':
        classes.push('bg-cyan-100 text-cyan-800')
        break
      default:
        classes.push('bg-gray-100 text-gray-800')
    }
  }

  return classes.join(' ')
})

// 文本样式类
const textClasses = computed(() => {
  const classes: string[] = []

  if (!formattedResult.value.isValid) {
    classes.push('text-red-600')
  } else if (formattedResult.value.isRelative) {
    classes.push('text-blue-600')
  } else {
    const textColor = theme.value.textColor || 'default'
    if (textColor !== 'default') {
      classes.push(`text-${textColor}-600`)
    }
  }

  return classes.join(' ')
})
</script>

<style scoped>
.date-renderer {
  display: inline-flex;
  align-items: center;
}

.date-renderer--text {
  font-size: 0.875rem;
}

.date-renderer--tooltip,
.date-renderer--relative-tooltip {
  font-size: 0.875rem;
}

.date-renderer--invalid {
  opacity: 0.75;
}

.date-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.date-badge {
  white-space: nowrap;
}

.date-tooltip {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.date-default {
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 悬停效果 */
.date-renderer:hover .date-tooltip {
  border-color: #4b5563;
}
</style>
