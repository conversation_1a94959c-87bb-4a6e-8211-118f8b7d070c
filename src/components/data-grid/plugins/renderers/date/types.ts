import type { BaseRendererConfig, BaseRendererProps } from '../../types'

/**
 * 日期格式选项
 */
export type DateFormat =
  | 'yyyy-MM-dd' // 2024-08-04
  | 'MM/dd/yyyy' // 08/04/2024
  | 'dd/MM/yyyy' // 04/08/2024
  | 'yyyy年MM月dd日' // 2024年08月04日
  | 'MMM dd, yyyy' // Aug 04, 2024
  | 'MMMM dd, yyyy' // August 04, 2024
  | 'relative' // 相对时间：2小时前、昨天、上周等
  | 'full-datetime' // 完整日期时间：2024-08-04 14:30:25
  | 'time-only' // 仅时间：14:30:25
  | 'smart' // 智能格式：今天显示时间，其他显示日期
  | ((date: Date) => string) // 自定义格式函数

/**
 * 显示变体
 */
export type DateVariant =
  | 'text' // 纯文本显示
  | 'badge' // 徽章样式
  | 'tooltip' // 带详细信息的工具提示
  | 'relative-tooltip' // 相对时间 + 完整时间的工具提示

/**
 * 时区配置
 */
export interface TimeZoneConfig {
  /** 时区标识符，如 'Asia/Shanghai', 'UTC', 'America/New_York' */
  timezone?: string
  /** 是否显示时区信息 */
  showTimezone?: boolean
  /** 时区显示格式：'short' (CST) | 'long' (China Standard Time) | 'offset' (+08:00) */
  timezoneFormat?: 'short' | 'long' | 'offset'
}

/**
 * 相对时间配置
 */
export interface RelativeTimeConfig {
  /** 相对时间的最大范围（天），超过则显示绝对时间 */
  maxDays?: number
  /** 自定义相对时间标签 */
  labels?: {
    justNow?: string // 刚刚
    minutesAgo?: string // {n}分钟前
    hoursAgo?: string // {n}小时前
    yesterday?: string // 昨天
    daysAgo?: string // {n}天前
    lastWeek?: string // 上周
    weeksAgo?: string // {n}周前
    lastMonth?: string // 上个月
    monthsAgo?: string // {n}个月前
    lastYear?: string // 去年
    yearsAgo?: string // {n}年前
    // 未来时间标签
    inMinutes?: string // {n}分钟后
    inHours?: string // {n}小时后
    tomorrow?: string // 明天
    inDays?: string // {n}天后
    nextWeek?: string // 下周
    inWeeks?: string // {n}周后
    nextMonth?: string // 下个月
    inMonths?: string // {n}个月后
    nextYear?: string // 明年
    inYears?: string // {n}年后
  }
}

/**
 * 工具提示配置
 */
export interface TooltipConfig {
  /** 是否启用工具提示 */
  enabled?: boolean
  /** 工具提示显示的格式 */
  format?: DateFormat
  /** 工具提示中显示的额外信息 */
  extraInfo?: {
    /** 显示星期几 */
    showWeekday?: boolean
    /** 显示相对时间 */
    showRelative?: boolean
    /** 显示时区信息 */
    showTimezone?: boolean
    /** 自定义额外信息 */
    customInfo?: (date: Date) => string[]
  }
}

/**
 * 主题样式配置
 */
export interface DateThemeConfig {
  /** 文本颜色 */
  textColor?:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'warning'
    | 'danger'
    | 'info'
    | 'muted'
  /** 徽章颜色（仅在 variant='badge' 时有效） */
  badgeColor?:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'warning'
    | 'danger'
    | 'info'
  /** 字体大小 */
  fontSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  /** 字体粗细 */
  fontWeight?: 'normal' | 'medium' | 'semibold' | 'bold'
  /** 自定义CSS类 */
  className?: string
}

/**
 * 验证配置
 */
export interface DateValidationConfig {
  /** 最小日期 */
  minDate?: Date
  /** 最大日期 */
  maxDate?: Date
  /** 无效日期时的显示文本 */
  invalidText?: string
  /** 空值时的显示文本 */
  emptyText?: string
}

/**
 * 日期渲染器配置接口
 */
export interface DateRendererConfig extends BaseRendererConfig {
  /** 日期格式 */
  format?: DateFormat
  /** 显示变体 */
  variant?: DateVariant
  /** 时区配置 */
  timezone?: TimeZoneConfig
  /** 相对时间配置 */
  relative?: RelativeTimeConfig
  /** 工具提示配置 */
  tooltip?: TooltipConfig
  /** 主题样式配置 */
  theme?: DateThemeConfig
  /** 验证配置 */
  validation?: DateValidationConfig
  /** 本地化设置 */
  locale?: string
  /** 是否启用智能格式化 */
  smartFormat?: boolean
  /** 点击事件处理 */
  onClick?: (date: Date, row: any) => void
  /** 双击事件处理 */
  onDoubleClick?: (date: Date, row: any) => void
}

/**
 * 日期渲染器属性接口
 */
export interface DateRendererProps extends BaseRendererProps {
  config?: DateRendererConfig
}

/**
 * 格式化结果接口
 */
export interface FormattedDateResult {
  /** 主要显示文本 */
  text: string
  /** 工具提示文本 */
  tooltip?: string
  /** 是否为相对时间 */
  isRelative?: boolean
  /** 是否为有效日期 */
  isValid: boolean
  /** 原始日期对象 */
  date?: Date
  /** 格式化的完整信息 */
  fullInfo?: {
    weekday?: string
    timezone?: string
    relative?: string
    absolute?: string
  }
}
