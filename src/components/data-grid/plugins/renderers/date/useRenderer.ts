import { computed } from 'vue'
import type {
  DateRendererProps,
  DateRendererConfig,
  FormattedDateResult,
  DateFormat,
  RelativeTimeConfig,
} from './types'

/**
 * 获取全局配置存储
 */
const getConfigStore = () => {
  return (window as any).__simpleConfigStore
}

/**
 * 日期渲染器组合函数
 */
export const useDateRenderer = (props: DateRendererProps) => {
  // 获取完整配置
  const config = computed<DateRendererConfig>(() => {
    if (props.configId) {
      const store = getConfigStore()
      return store?.get?.(props.configId) || {}
    }
    return props.config || {}
  })

  // 获取原始值
  const rawValue = computed(() => {
    return props.field ? props.row?.[props.field] : props.value
  })

  // 解析日期
  const parsedDate = computed<Date | null>(() => {
    const value = rawValue.value

    // 处理 null、undefined、空字符串等空值
    if (
      !value ||
      value === '' ||
      (typeof value === 'string' && value.trim() === '')
    ) {
      return null
    }

    if (value instanceof Date) {
      return isNaN(value.getTime()) ? null : value
    }

    if (typeof value === 'string') {
      const parsed = new Date(value)
      return isNaN(parsed.getTime()) ? null : parsed
    }

    if (typeof value === 'number') {
      // 处理时间戳，支持秒和毫秒
      const timestamp = value < 1e12 ? value * 1000 : value
      const parsed = new Date(timestamp)
      return isNaN(parsed.getTime()) ? null : parsed
    }

    return null
  })

  // 检查日期有效性
  const isValidDate = computed(() => parsedDate.value !== null)

  // 检查日期是否在验证范围内
  const isInValidRange = computed(() => {
    const date = parsedDate.value
    if (!date) return false

    const { minDate, maxDate } = config.value.validation || {}

    if (minDate && date < minDate) return false
    if (maxDate && date > maxDate) return false

    return true
  })

  /**
   * 格式化相对时间
   */
  const formatRelativeTime = (
    date: Date,
    relativeConfig?: RelativeTimeConfig
  ): string => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMinutes = Math.floor(Math.abs(diffMs) / (1000 * 60))
    const diffHours = Math.floor(Math.abs(diffMs) / (1000 * 60 * 60))
    const diffDays = Math.floor(Math.abs(diffMs) / (1000 * 60 * 60 * 24))
    const diffWeeks = Math.floor(diffDays / 7)
    const diffMonths = Math.floor(diffDays / 30)
    const diffYears = Math.floor(diffDays / 365)

    const labels = {
      justNow: '刚刚',
      minutesAgo: '{n}分钟前',
      hoursAgo: '{n}小时前',
      yesterday: '昨天',
      daysAgo: '{n}天前',
      lastWeek: '上周',
      weeksAgo: '{n}周前',
      lastMonth: '上个月',
      monthsAgo: '{n}个月前',
      lastYear: '去年',
      yearsAgo: '{n}年前',
      // 未来时间
      inMinutes: '{n}分钟后',
      inHours: '{n}小时后',
      tomorrow: '明天',
      inDays: '{n}天后',
      nextWeek: '下周',
      inWeeks: '{n}周后',
      nextMonth: '下个月',
      inMonths: '{n}个月后',
      nextYear: '明年',
      inYears: '{n}年后',
      ...relativeConfig?.labels,
    }

    const maxDays = relativeConfig?.maxDays || 90 // 增加到90天，更实用

    // 未来时间处理
    if (diffMs < 0) {
      if (diffMinutes < 1) {
        return labels.justNow
      } else if (diffMinutes < 60) {
        return labels.inMinutes.replace('{n}', diffMinutes.toString())
      } else if (diffHours < 24) {
        return labels.inHours.replace('{n}', diffHours.toString())
      } else if (diffDays === 1) {
        return labels.tomorrow
      } else if (diffDays < 7) {
        return labels.inDays.replace('{n}', diffDays.toString())
      } else if (diffWeeks === 1) {
        return labels.nextWeek
      } else if (diffWeeks < 5) {
        return labels.inWeeks.replace('{n}', diffWeeks.toString())
      } else if (diffMonths === 1) {
        return labels.nextMonth
      } else if (diffMonths < 12) {
        return labels.inMonths.replace('{n}', diffMonths.toString())
      } else if (diffYears === 1) {
        return labels.nextYear
      } else {
        return labels.inYears.replace('{n}', diffYears.toString())
      }
    }

    // 过去时间处理
    if (diffMinutes < 1) {
      return labels.justNow
    } else if (diffMinutes < 60) {
      return labels.minutesAgo.replace('{n}', diffMinutes.toString())
    } else if (diffHours < 24) {
      return labels.hoursAgo.replace('{n}', diffHours.toString())
    } else if (diffDays === 1) {
      return labels.yesterday
    } else if (diffDays < 7) {
      return labels.daysAgo.replace('{n}', diffDays.toString())
    } else if (diffWeeks === 1) {
      return labels.lastWeek
    } else if (diffWeeks < 5) {
      return labels.weeksAgo.replace('{n}', diffWeeks.toString())
    } else if (diffMonths === 1) {
      return labels.lastMonth
    } else if (diffMonths < 12) {
      return labels.monthsAgo.replace('{n}', diffMonths.toString())
    } else if (diffYears === 1) {
      return labels.lastYear
    } else if (diffDays <= maxDays) {
      return labels.yearsAgo.replace('{n}', diffYears.toString())
    }

    // 超过最大范围，返回空字符串让调用者使用绝对时间
    return ''
  }

  /**
   * 格式化绝对时间
   */
  const formatAbsoluteTime = (
    date: Date,
    format: DateFormat,
    locale?: string
  ): string => {
    if (typeof format === 'function') {
      return format(date)
    }

    const options: Intl.DateTimeFormatOptions = {}
    const timeZone = config.value.timezone?.timezone

    if (timeZone) {
      options.timeZone = timeZone
    }

    switch (format) {
      case 'yyyy-MM-dd':
        return date
          .toLocaleDateString(locale || 'zh-CN', {
            ...options,
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
          })
          .replace(/\//g, '-')

      case 'MM/dd/yyyy':
        return date.toLocaleDateString('en-US', {
          ...options,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        })

      case 'dd/MM/yyyy':
        return date.toLocaleDateString('en-GB', {
          ...options,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        })

      case 'yyyy年MM月dd日':
        return date.toLocaleDateString('zh-CN', {
          ...options,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        })

      case 'MMM dd, yyyy':
        return date.toLocaleDateString('en-US', {
          ...options,
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        })

      case 'MMMM dd, yyyy':
        return date.toLocaleDateString('en-US', {
          ...options,
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        })

      case 'full-datetime':
        return date.toLocaleString(locale || 'zh-CN', {
          ...options,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false,
        })

      case 'time-only':
        return date.toLocaleTimeString(locale || 'zh-CN', {
          ...options,
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false,
        })

      case 'smart':
        const now = new Date()
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        const dateDay = new Date(
          date.getFullYear(),
          date.getMonth(),
          date.getDate()
        )

        if (dateDay.getTime() === today.getTime()) {
          // 今天显示时间
          return date.toLocaleTimeString(locale || 'zh-CN', {
            ...options,
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          })
        } else {
          // 其他日期显示日期
          return date.toLocaleDateString(locale || 'zh-CN', {
            ...options,
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
          })
        }

      default:
        return date.toLocaleDateString(locale || 'zh-CN', options)
    }
  }

  /**
   * 格式化日期
   */
  const formattedResult = computed<FormattedDateResult>(() => {
    const date = parsedDate.value
    const originalValue = rawValue.value

    // 处理空值（null、undefined、空字符串）
    if (
      !originalValue ||
      originalValue === '' ||
      (typeof originalValue === 'string' && originalValue.trim() === '')
    ) {
      return {
        text: config.value.validation?.emptyText || '-',
        isValid: false,
        isRelative: false,
      }
    }

    // 处理无效日期
    if (!date || !isValidDate.value) {
      return {
        text: config.value.validation?.invalidText || '无效日期',
        isValid: false,
        isRelative: false,
      }
    }

    // 处理超出范围的日期
    if (!isInValidRange.value) {
      return {
        text: config.value.validation?.invalidText || '日期超出范围',
        isValid: false,
        isRelative: false,
        date,
      }
    }

    const format = config.value.format || 'yyyy-MM-dd'
    const locale = config.value.locale
    let text: string
    let isRelative = false

    // 相对时间格式
    if (format === 'relative') {
      const relativeText = formatRelativeTime(date, config.value.relative)
      if (relativeText) {
        text = relativeText
        isRelative = true
      } else {
        // 超出相对时间范围，使用默认格式
        text = formatAbsoluteTime(date, 'yyyy-MM-dd', locale)
      }
    } else {
      text = formatAbsoluteTime(date, format, locale)
    }

    // 生成工具提示
    let tooltip: string | undefined
    const tooltipConfig = config.value.tooltip

    if (
      tooltipConfig?.enabled ||
      config.value.variant === 'tooltip' ||
      config.value.variant === 'relative-tooltip'
    ) {
      const tooltipFormat = tooltipConfig?.format || 'full-datetime'
      tooltip = formatAbsoluteTime(date, tooltipFormat, locale)

      // 添加额外信息
      if (tooltipConfig?.extraInfo) {
        const extraInfo: string[] = []

        if (tooltipConfig.extraInfo.showWeekday) {
          const weekday = date.toLocaleDateString(locale || 'zh-CN', {
            weekday: 'long',
          })
          extraInfo.push(`${weekday}`)
        }

        if (tooltipConfig.extraInfo.showRelative && !isRelative) {
          const relativeText = formatRelativeTime(date, config.value.relative)
          if (relativeText) {
            extraInfo.push(relativeText)
          }
        }

        if (
          tooltipConfig.extraInfo.showTimezone &&
          config.value.timezone?.timezone
        ) {
          const tz = config.value.timezone
          let tzText = tz.timezone

          if (tz.timezoneFormat === 'short') {
            tzText =
              date
                .toLocaleDateString(locale, { timeZoneName: 'short' })
                .split(' ')
                .pop() || tz.timezone
          } else if (tz.timezoneFormat === 'long') {
            tzText =
              date
                .toLocaleDateString(locale, { timeZoneName: 'long' })
                .split(' ')
                .slice(1)
                .join(' ') || tz.timezone
          } else if (tz.timezoneFormat === 'offset') {
            const offset = date.getTimezoneOffset()
            const hours = Math.floor(Math.abs(offset) / 60)
            const minutes = Math.abs(offset) % 60
            tzText = `${offset <= 0 ? '+' : '-'}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
          }

          extraInfo.push(tzText)
        }

        if (tooltipConfig.extraInfo.customInfo) {
          extraInfo.push(...tooltipConfig.extraInfo.customInfo(date))
        }

        if (extraInfo.length > 0) {
          tooltip += '\n' + extraInfo.join(' | ')
        }
      }
    }

    // 生成完整信息
    const fullInfo = {
      weekday: date.toLocaleDateString(locale || 'zh-CN', { weekday: 'long' }),
      timezone: config.value.timezone?.timezone,
      relative: formatRelativeTime(date, config.value.relative),
      absolute: formatAbsoluteTime(date, 'full-datetime', locale),
    }

    return {
      text,
      tooltip,
      isRelative,
      isValid: true,
      date,
      fullInfo,
    }
  })

  // 主要显示文本
  const displayText = computed(() => formattedResult.value.text)

  // 工具提示文本
  const tooltipText = computed(() => formattedResult.value.tooltip)

  // 变体类型
  const variant = computed(() => config.value.variant || 'text')

  // 主题配置
  const theme = computed(() => config.value.theme || {})

  // 样式类
  const cssClasses = computed(() => {
    const classes: string[] = []
    const themeConfig = theme.value

    // 基础类
    classes.push('date-renderer')

    // 变体类
    classes.push(`date-renderer--${variant.value}`)

    // 主题类
    if (themeConfig.textColor) {
      classes.push(`text-${themeConfig.textColor}`)
    }

    if (themeConfig.fontSize) {
      classes.push(`text-${themeConfig.fontSize}`)
    }

    if (themeConfig.fontWeight) {
      classes.push(`font-${themeConfig.fontWeight}`)
    }

    if (variant.value === 'badge' && themeConfig.badgeColor) {
      classes.push(`badge-${themeConfig.badgeColor}`)
    }

    // 自定义类
    if (themeConfig.className) {
      classes.push(themeConfig.className)
    }

    // 状态类
    if (!formattedResult.value.isValid) {
      classes.push('date-renderer--invalid')
    }

    if (formattedResult.value.isRelative) {
      classes.push('date-renderer--relative')
    }

    return classes.join(' ')
  })

  // 事件处理
  const handleClick = () => {
    const date = parsedDate.value
    if (date && config.value.onClick) {
      config.value.onClick(date, props.row)
    }
  }

  const handleDoubleClick = () => {
    const date = parsedDate.value
    if (date && config.value.onDoubleClick) {
      config.value.onDoubleClick(date, props.row)
    }
  }

  return {
    // 基础数据
    rawValue,
    parsedDate,
    isValidDate,
    isInValidRange,

    // 格式化结果
    formattedResult,
    displayText,
    tooltipText,

    // 配置和样式
    config,
    variant,
    theme,
    cssClasses,

    // 事件处理
    handleClick,
    handleDoubleClick,
  }
}
