<template>
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger as-child>
        <Button
          variant="link"
          :class="linkClasses"
          @click="handleClick"
          @keydown.enter="handleClick"
          @keydown.space.prevent="handleClick"
          :title="tooltipText"
          :aria-label="ariaLabel"
          :disabled="!isInteractive"
          class="h-auto w-full p-0 pr-1 gap-0 text-left justify-start no-underline hover:underline transition-colors duration-200"
        >
          <Icon
            v-if="typeIcon"
            :icon="typeIcon"
            class="mr-1 inline-block w-4 h-4"
          />
          <span class="w-full truncate">{{ displayText }}</span>
          <Icon
            v-if="showExternalIcon && shouldShowExternalIcon"
            icon="mdi:open-in-new"
            class="ml-1 inline-block text-xs w-3 h-3"
          />
        </Button>
      </TooltipTrigger>
      <TooltipContent>{{ displayText }}</TooltipContent>
    </Tooltip>
  </TooltipProvider>
</template>

<script setup lang="ts">
defineOptions({
  name: '<PERSON><PERSON><PERSON><PERSON>',
})

import { Icon } from '@iconify/vue'
import { Button } from '@/components/ui/button'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import type { LinkRendererProps } from './types'
import { useLinkRenderer } from './useRenderer'

const props = defineProps<LinkRendererProps>()

const {
  typeIcon,
  showExternalIcon,
  shouldShowExternalIcon,
  isInteractive,
  displayText,
  tooltipText,
  ariaLabel,
  linkClasses,
  handleClick,
} = useLinkRenderer(props)
</script>
