import { BaseRendererConfig, BaseRendererProps } from '../../types'

/**
 * 链接渲染器配置接口
 */
export interface LinkRendererConfig extends BaseRendererConfig {
  /** 链接地址 */
  href?: string | ((row: any) => string)
  /** 打开方式 */
  target?: '_blank' | '_self' | '_parent' | '_top'
  /** 链接类型 */
  linkType?: 'url' | 'mail' | 'phone' | 'auto'
  /** 是否显示外部链接图标 */
  showExternal?: boolean
  /** 是否显示类型图标 */
  showTypeIcon?: boolean
  /** 是否截断显示 */
  truncate?: boolean
  /** 最大长度 */
  maxLength?: number
  /** 显示模板 */
  template?: string
  /** 点击事件处理器 */
  onClick?: (row: any, event: Event) => boolean | void
}

/**
 * 链接渲染器属性接口
 */
export interface LinkRendererProps extends BaseRendererProps {
  config?: LinkRendererConfig
}
