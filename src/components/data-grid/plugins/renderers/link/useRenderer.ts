/**
 * 链接渲染器逻辑钩子
 */

import { computed } from 'vue'
import { cn } from '@/lib/utils'
import type { LinkRendererProps } from './types'

export const useLinkRenderer = (props: LinkRendererProps) => {
  // 工具函数：检测链接类型
  const detectLinkType = (
    value: string
  ): 'url' | 'mail' | 'phone' | 'unknown' => {
    if (!value) return 'unknown'

    const trimmed = value.trim()

    // 邮箱检测
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (emailRegex.test(trimmed)) return 'mail'

    // 电话检测
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{7,20}$/
    if (phoneRegex.test(trimmed.replace(/\s/g, ''))) return 'phone'

    // URL检测
    try {
      new URL(trimmed)
      return 'url'
    } catch {
      // 检测没有协议的URL
      const urlRegex =
        /^(www\.)?[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
      if (urlRegex.test(trimmed)) return 'url'
    }

    return 'unknown'
  }

  // 工具函数：生成链接URL
  const generateUrl = (
    value: string,
    type: string,
    hrefTemplate?: string | ((row: any) => string)
  ): string => {
    if (hrefTemplate) {
      if (typeof hrefTemplate === 'function') {
        return hrefTemplate(props.row)
      }
      // 处理模板字符串
      return hrefTemplate.replace(/\{([^}]+)\}/g, (match, key) => {
        if (key === 'value') return value
        if (key.startsWith('row.')) {
          const rowKey = key.substring(4)
          return String(props.row?.[rowKey] || '')
        }
        return match
      })
    }

    switch (type) {
      case 'mail':
        return `mailto:${value}`
      case 'phone':
        // 清理电话号码格式：移除所有非数字字符，保留 + 号（国际号码）
        const cleanPhone = value.replace(/[^\d+]/g, '')
        return `tel:${cleanPhone}`
      case 'url':
        return normalizeUrl(value)
      default:
        return value
    }
  }

  // 工具函数：标准化URL
  const normalizeUrl = (url: string): string => {
    if (!url) return '#'

    // 如果已经有协议，直接返回
    if (/^https?:\/\//.test(url)) return url

    // 如果是相对路径，直接返回
    if (url.startsWith('/')) return url

    // 其他情况加上http://
    return `http://${url}`
  }

  // 获取显示值
  const value = computed(() => {
    return props.field ? props.row?.[props.field] : props.value
  })

  // 自动检测链接类型
  const detectedType = computed(() => {
    const currentValue = value.value
    if (!currentValue) return props.config?.linkType || 'url'

    if (props.config?.linkType === 'auto' || !props.config?.linkType) {
      return detectLinkType(String(currentValue))
    }

    return props.config.linkType
  })

  // 生成链接URL
  const linkUrl = computed(() => {
    const currentValue = value.value
    if (!currentValue) return '#'

    return generateUrl(
      String(currentValue),
      detectedType.value,
      props.config?.href
    )
  })

  // 获取类型图标
  const typeIcon = computed(() => {
    if (!props.config?.showTypeIcon) return null

    switch (detectedType.value) {
      case 'mail':
        return 'mdi:email'
      case 'phone':
        return 'mdi:phone'
      case 'url':
        return 'mdi:link'
      default:
        return 'mdi:open-in-new'
    }
  })

  const target = computed(() => props.config?.target || '_blank')
  const showExternalIcon = computed(() => props.config?.showExternal !== false)
  const shouldShowExternalIcon = computed(() => {
    return (
      showExternalIcon.value &&
      detectedType.value === 'url' &&
      target.value === '_blank'
    )
  })
  const maxLength = computed(() => props.config?.maxLength || 30)
  const isInteractive = computed(() => props.config?.interactive !== false)

  // 处理显示文本
  const displayText = computed(() => {
    let text = String(value.value || '')

    // 如果有模板配置，使用模板渲染
    if (props.config?.template) {
      text = props.config.template.replace(/\$\{(\w+)\}/g, (match, key) => {
        if (key === 'value') {
          return String(value.value || '')
        }
        if (key === 'field' && props.field) {
          return String(props.row?.[props.field] || '')
        }
        // 支持访问 row 中的其他字段
        return String(props.row?.[key] || match)
      })
    }

    return text
  })

  // 工具提示文本
  const tooltipText = computed(() => {
    const currentValue = value.value
    if (!currentValue) return ''
    return ''
  })

  // 无障碍标签
  const ariaLabel = computed(() => {
    const currentValue = value.value
    if (!currentValue) return ''

    const typeText =
      {
        url: '链接',
        mail: '邮箱',
        phone: '电话',
      }[detectedType.value] || '链接'

    return `${typeText}: ${currentValue}`
  })

  // 链接样式
  const linkClasses = computed(() => {
    const classes = []

    if (!isInteractive.value) {
      classes.push('pointer-events-none')
    }

    // 添加自定义样式类
    if (props.config?.className) {
      classes.push(props.config.className)
    }

    return cn(
      'text-blue-600 hover:text-blue-800 inline-flex items-center',
      classes.join(' ')
    )
  })

  // 处理点击事件
  const handleClick = () => {
    if (!isInteractive.value) return

    const url = linkUrl.value
    if (!url || url === '#') return

    let shouldContinue = true

    // 如果有自定义点击处理函数
    if (props.config?.onClick) {
      const result = props.config.onClick(props.row, new Event('click'))
      // 如果返回值明确为 false，则阻止默认行为
      if (result === false) {
        shouldContinue = false
      }
    }

    // 如果应该继续执行默认行为
    if (shouldContinue) {
      if (detectedType.value === 'mail' || detectedType.value === 'phone') {
        window.location.href = url
      } else {
        window.open(url, target.value)
      }
    }
  }

  return {
    value,
    detectedType,
    linkUrl,
    typeIcon,
    target,
    showExternalIcon,
    shouldShowExternalIcon,
    maxLength,
    isInteractive,
    displayText,
    tooltipText,
    ariaLabel,
    linkClasses,
    handleClick,
  }
}
