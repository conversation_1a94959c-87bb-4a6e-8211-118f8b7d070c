export { default as Number<PERSON>enderer } from './renderer.vue'
export * from './types'
export * from './useRenderer'

// 渲染器注册配置
export const numberRendererConfig = {
  name: 'number',
  component: () => import('./renderer.vue'),
  description: '数值渲染器，支持多种数值格式和货币换算',
  configSchema: {
    type: 'object',
    properties: {
      type: {
        type: 'string',
        enum: [
          'integer',
          'decimal',
          'currency',
          'percentage',
          'ratio',
          'scientific',
        ],
        default: 'decimal',
        description: '数值类型',
      },
      variant: {
        type: 'string',
        enum: ['text', 'badge', 'colored', 'progress'],
        default: 'text',
        description: '显示变体',
      },
      format: {
        type: 'object',
        properties: {
          decimalPlaces: {
            type: 'number',
            description: '小数位数',
          },
          useGrouping: {
            type: 'boolean',
            default: true,
            description: '是否使用千分位分隔符',
          },
        },
      },
      currency: {
        type: 'object',
        properties: {
          code: {
            type: 'string',
            description: '货币代码',
          },
          exchange_rate: {
            type: 'string',
            description: '汇率',
          },
          symbol: {
            type: 'string',
            description: '货币符号',
          },
        },
        description: '货币配置',
      },
      showDualCurrency: {
        type: 'boolean',
        default: false,
        description: '是否显示双行货币',
      },
      colors: {
        type: 'object',
        properties: {
          positiveColor: { type: 'string', description: '正数颜色' },
          negativeColor: { type: 'string', description: '负数颜色' },
          zeroColor: { type: 'string', description: '零值颜色' },
        },
      },
      progress: {
        type: 'object',
        properties: {
          min: { type: 'number', default: 0, description: '最小值' },
          max: { type: 'number', default: 100, description: '最大值' },
          color: { type: 'string', default: 'blue', description: '进度条颜色' },
          showText: {
            type: 'boolean',
            default: true,
            description: '是否显示文本',
          },
        },
      },
      nullText: {
        type: 'string',
        default: '--',
        description: '空值显示文本',
      },
    },
  },
}
