<template>
  <div class="inline-flex items-center">
    <!-- Badge 变体 -->
    <div v-if="config.variant === 'badge'" class="flex flex-col gap-1">
      <!-- 当启用双币显示时，显示为两个独立的badge -->
      <template
        v-if="config.showDualCurrency && formattedData.secondaryDisplay"
      >
        <Badge :class="badgeClasses">
          {{ formattedData.primaryDisplay }}
        </Badge>
        {{ formattedData.secondaryDisplay }}
      </template>
      <!-- 单币显示或无辅显示时，显示为单个badge -->
      <Badge v-else :class="badgeClasses">
        {{ formattedData.primaryDisplay }}
      </Badge>
    </div>

    <!-- Progress 变体 -->
    <div v-else-if="config.variant === 'progress'" class="w-full">
      <div
        class="flex items-center justify-between mb-1"
        v-if="progressConfig.showText"
      >
        <span class="text-sm font-medium" :class="numberClasses">
          {{ formattedData.primaryDisplay }}
        </span>
        <span
          class="text-xs text-gray-500"
          v-if="formattedData.secondaryDisplay"
        >
          ({{ formattedData.secondaryDisplay }})
        </span>
      </div>
      <div
        class="w-full bg-gray-200 rounded-full"
        :style="{ height: progressConfig.height }"
      >
        <div
          class="rounded-full transition-all duration-300 ease-in-out"
          :class="[
            progressConfig.color === 'blue'
              ? 'bg-blue-600'
              : progressConfig.color === 'green'
                ? 'bg-green-600'
                : progressConfig.color === 'red'
                  ? 'bg-red-600'
                  : progressConfig.color === 'yellow'
                    ? 'bg-yellow-600'
                    : 'bg-blue-600',
          ]"
          :style="{
            width: `${progressConfig.value}%`,
            height: progressConfig.height,
          }"
        />
      </div>
    </div>

    <!-- Colored/Text 变体 -->
    <div v-else class="flex flex-col">
      <div :class="[numberClasses, 'font-medium', 'leading-tight']">
        {{ formattedData.primaryDisplay }}
      </div>
      <div
        v-if="formattedData.secondaryDisplay"
        class="text-xs text-gray-500 dark:text-gray-400 leading-tight mt-0.5"
      >
        {{ formattedData.secondaryDisplay }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'NumberRenderer',
})

import { Badge } from '@/components/ui/badge'
import type { NumberRendererProps } from './types'
import { useNumberRenderer } from './useRenderer'

const props = defineProps<NumberRendererProps>()

const { config, formattedData, numberClasses, badgeClasses, progressConfig } =
  useNumberRenderer(props)
</script>
