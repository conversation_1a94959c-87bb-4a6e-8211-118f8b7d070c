# API 动态渲染详细说明

## 概述

API 动态渲染是关系列增强功能的核心特性，它能够：
- 自动调用指定的 API 获取完整的关系数据
- 根据 API 返回的元数据自动生成美观的详情界面
- 智能识别字段类型并选择合适的展示组件
- 无需手动编写业务组件即可实现丰富的数据展示

## 工作原理

### 1. 数据获取流程

```mermaid
graph TD
    A[点击关系列] --> B[获取关系数据中的ID]
    B --> C[调用 getApi(moduleModel)]
    C --> D[并行调用 getMetadata 和 getDetail]
    D --> E[解析元数据字段定义]
    E --> F[根据字段类型渲染界面]
```

### 2. 字段类型识别

系统会根据以下规则自动识别字段类型：

#### 元数据类型优先
- `integer/float/decimal` → 数字类型
- `boolean` → 布尔类型
- `date` → 日期类型
- `datetime/timestamp` → 日期时间类型
- `enum` → 枚举类型
- `relation` → 关系类型

#### 字段名称推断
- 包含 `email` → 邮箱类型
- 包含 `phone/tel` → 电话类型
- 包含 `url/link` → URL 类型
- 包含 `status` → 状态类型
- 包含 `amount/price/cost` → 货币类型
- 包含 `date/time` → 日期时间类型

## 配置参数详解

### moduleModel
- **类型**: `string`
- **格式**: `"module/model"`
- **示例**: `"hr/employee"`, `"crm/customer"`, `"inventory/product"`
- **说明**: 指定要调用的 API 模块路径

### apiDataKey
- **类型**: `string`
- **默认值**: `"id"`
- **说明**: 从关系数据中获取用于调用 `getDetail(id)` 的字段名
- **示例**: 
  ```typescript
  // 如果关系数据结构为 { customer_id: 123, customer_name: "张三" }
  apiDataKey: 'customer_id'
  ```

### enableApiRendering
- **类型**: `boolean`
- **默认值**: `true`（当配置了 `moduleModel` 时）
- **说明**: 是否启用 API 动态渲染
- **使用场景**: 当同时配置了 `moduleModel` 和 `component` 时，可以选择使用哪种渲染方式

## 界面布局规则

### 字段分组
系统会自动将字段分为以下组：

1. **基本信息**: 包含 `name`, `title`, `code`, `no` 等字段
2. **联系信息**: 包含 `email`, `phone`, `address` 等字段
3. **业务信息**: 包含 `amount`, `price`, `sales`, `revenue` 等字段
4. **系统信息**: 包含 `created`, `updated`, `status` 等字段
5. **其他信息**: 不属于以上分类的字段

### 关系字段处理
- 关系字段会单独显示在"关联信息"区域
- 自动提取关系对象的显示字段（name, title, nick_name 等）

## 字段渲染组件

### 文本类型
```html
<span class="text-gray-900">普通文本</span>
```

### 数字类型
```html
<span class="text-gray-900 font-mono">1,234.56</span>
```

### 货币类型
```html
<span class="text-green-600 font-semibold">¥1,234.56</span>
```

### 日期类型
```html
<span class="text-blue-600">2024-01-15</span>
```

### 布尔类型
```html
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
  <svg>✓</svg> 是
</span>
```

### 枚举类型
```html
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
  枚举值
</span>
```

### 状态类型
```html
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
  <span class="w-2 h-2 rounded-full bg-green-500 mr-1"></span>
  活跃
</span>
```

### 邮箱类型
```html
<a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800 underline">
  <EMAIL>
</a>
```

### 电话类型
```html
<a href="tel:13800138000" class="text-green-600 hover:text-green-800 underline">
  13800138000
</a>
```

## API 要求

### 必需方法
- `getMetadata()`: 返回字段元数据定义
- `getDetail(id)`: 根据 ID 获取详情数据

### 元数据格式
```typescript
{
  fields: [
    {
      name: "name",
      label: "姓名",
      type: "string",
      required: true
    },
    {
      name: "email",
      label: "邮箱",
      type: "string"
    },
    {
      name: "status",
      label: "状态",
      type: "enum",
      enum_info: {
        enum_values: {
          "active": "活跃",
          "inactive": "非活跃"
        }
      }
    }
  ]
}
```

## 性能优化

### 1. 并行请求
- 同时调用 `getMetadata()` 和 `getDetail(id)`
- 减少等待时间

### 2. 缓存机制
- API 实例会被 `ApiService` 自动缓存
- 元数据会被缓存，避免重复请求

### 3. 错误处理
- 提供友好的错误提示
- 支持重试机制

## 最佳实践

### 1. 模块命名
- 使用清晰的模块路径：`hr/employee`, `crm/customer`
- 保持命名一致性

### 2. 字段设计
- 为字段提供清晰的 `label`
- 合理使用字段类型定义
- 为枚举字段提供 `enum_info`

### 3. 性能考虑
- 避免在详情数据中包含过多不必要的字段
- 合理设计 API 响应结构

### 4. 用户体验
- 提供有意义的标题和副标题
- 使用合适的弹窗尺寸
- 添加加载状态和错误处理

## 故障排除

### 1. API 调用失败
- 检查 `moduleModel` 路径是否正确
- 确认对应的 API 模块存在
- 查看控制台错误信息

### 2. 数据显示异常
- 检查 `apiDataKey` 配置是否正确
- 确认关系数据中包含指定的 ID 字段
- 验证 API 返回的数据格式

### 3. 字段类型识别错误
- 在元数据中明确指定字段类型
- 检查字段命名是否符合推断规则
- 考虑使用自定义组件处理特殊字段

### 4. 界面布局问题
- 检查字段分组逻辑
- 确认字段数据存在且不为空
- 调整弹窗尺寸设置
