# 关系列 API 动态渲染调试指南

## 问题排查步骤

### 1. 检查点击事件是否触发

打开浏览器开发者工具的控制台，点击销售员列的链接，应该看到以下日志：

```
关系列点击事件触发: {
  data: { id: 101, nick_name: "小李", employee_no: "EMP001", ... },
  config: { moduleModel: "hr/employee", detailMode: "drawer", ... },
  hasModuleModel: true,
  hasComponent: false,
  hasRoutePath: false
}
```

**如果没有看到这个日志**：
- 检查关系列是否正确配置了 `variant: 'link'`
- 检查数据中是否有 `sales` 字段
- 检查 `shouldEnableClick` 计算属性是否返回 `true`

### 2. 检查详情打开流程

如果点击事件触发了，应该看到：

```
准备打开单项详情: {
  item: { id: 101, nick_name: "小李", ... },
  detailMode: "drawer",
  moduleModel: "hr/employee",
  component: undefined
}

使用详情模式: drawer
```

**如果没有看到这些日志**：
- 检查 `openDetail` 方法是否被调用
- 检查数据是否为空或格式不正确

### 3. 检查抽屉打开流程

应该看到：

```
准备打开抽屉详情: {
  data: { id: 101, nick_name: "小李", ... },
  config: { moduleModel: "hr/employee", ... },
  hasModuleModel: true
}

调用 openDrawerForm: {
  title: "销售员详情 - 小李",
  componentProps: { relationData: {...}, row: {...}, config: {...} }
}
```

**如果没有看到这些日志**：
- 检查 `openDrawerDetail` 方法是否被调用
- 检查 `useGlobDrawerForm` 导入是否成功

### 4. 检查 RelationDetailViewer 组件

抽屉打开后，应该看到：

```
RelationDetailViewer shouldUseApiRendering: {
  result: true,
  moduleModel: "hr/employee",
  component: undefined,
  enableApiRendering: undefined,
  relationData: { id: 101, nick_name: "小李", ... }
}
```

**如果 `result` 为 `false`**：
- 检查 `moduleModel` 是否正确传递
- 检查 `component` 是否意外被设置
- 检查 `enableApiRendering` 是否被设置为 `false`

### 5. 检查 ApiDynamicRenderer 组件

如果 `shouldUseApiRendering` 为 `true`，应该看到：

```
ApiDynamicRenderer mounted: {
  moduleModel: "hr/employee",
  relationData: { id: 101, nick_name: "小李", ... },
  apiDataKey: "id",
  config: { moduleModel: "hr/employee", ... }
}
```

然后应该看到 API 调用：

```
API 动态渲染数据加载完成: {
  moduleModel: "hr/employee",
  metadata: { fields: [...] },
  detail: { id: 101, name: "李销售", ... }
}
```

**如果没有看到 API 调用成功的日志**：
- 检查是否看到错误日志：`API 动态渲染数据加载失败`
- 检查 `getApi('hr/employee')` 是否能正确返回 API 实例
- 检查 API 实例是否有 `getMetadata` 和 `getDetail` 方法

## 常见问题解决

### 问题1：点击没有反应

**可能原因**：
1. 关系列配置不正确
2. 数据格式不正确
3. 点击事件没有绑定

**解决方案**：
```typescript
// 确保配置正确
columnHelper.relation('sales', '销售员', {
  displayField: 'nick_name',
  variant: 'link', // 必须设置为 'link' 才能点击
  detailMode: 'drawer',
  moduleModel: 'hr/employee',
})

// 确保数据格式正确
const testData = [
  {
    id: 1,
    sales: { // 关系字段
      id: 101,
      nick_name: '小李',
      employee_no: 'EMP001'
    }
  }
]
```

### 问题2：抽屉打开但没有内容

**可能原因**：
1. `RelationDetailViewer` 组件没有正确判断渲染模式
2. `ApiDynamicRenderer` 组件没有被渲染

**解决方案**：
检查控制台日志，确认 `shouldUseApiRendering` 返回 `true`

### 问题3：API 调用失败

**可能原因**：
1. `moduleModel` 路径不正确
2. API 模块不存在
3. API 方法不存在

**解决方案**：
```typescript
// 检查 API 模块是否存在
try {
  const api = await getApi('hr/employee')
  console.log('API 实例:', api)
  console.log('getMetadata 方法:', typeof api.getMetadata)
  console.log('getDetail 方法:', typeof api.getDetail)
} catch (error) {
  console.error('API 获取失败:', error)
}
```

### 问题4：数据 ID 获取失败

**可能原因**：
1. `apiDataKey` 配置不正确
2. 关系数据中没有对应的 ID 字段

**解决方案**：
```typescript
// 检查数据结构
console.log('关系数据:', relationData)
console.log('ID 字段值:', relationData[apiDataKey || 'id'])

// 如果 ID 字段名不是 'id'，需要配置 apiDataKey
columnHelper.relation('sales', '销售员', {
  moduleModel: 'hr/employee',
  apiDataKey: 'employee_id', // 自定义 ID 字段名
})
```

## 测试数据示例

```javascript
const testData = [
  {
    id: 1,
    customer_name: '张三公司',
    order_no: 'ORD-2024-001',
    total_amount: 15000,
    
    // 销售员关系数据
    sales: {
      id: 101, // 这个 ID 会被用来调用 getDetail(101)
      nick_name: '小李',
      employee_no: 'EMP001'
    }
  }
]
```

## 预期的完整日志流程

```
1. 关系列点击事件触发: { data: {...}, config: {...}, hasModuleModel: true }
2. 准备打开单项详情: { item: {...}, detailMode: "drawer", moduleModel: "hr/employee" }
3. 使用详情模式: drawer
4. 准备打开抽屉详情: { data: {...}, config: {...}, hasModuleModel: true }
5. 调用 openDrawerForm: { title: "销售员详情 - 小李", componentProps: {...} }
6. RelationDetailViewer shouldUseApiRendering: { result: true, moduleModel: "hr/employee" }
7. ApiDynamicRenderer mounted: { moduleModel: "hr/employee", relationData: {...} }
8. API 动态渲染数据加载完成: { moduleModel: "hr/employee", metadata: {...}, detail: {...} }
```

如果在任何一步出现问题，请检查对应的配置和数据格式。
