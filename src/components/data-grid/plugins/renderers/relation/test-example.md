# 关系列功能测试示例

## 测试数据结构

为了测试关系列功能（包括 API 动态渲染），你的数据应该包含以下结构：

```javascript
const testData = [
  {
    id: 1,
    customer_name: '张三公司',
    address: '北京市朝阳区',
    email: '<PERSON><PERSON><PERSON>@example.com',
    phone: '13800138000',
    status: 'active',
    order_no: 'ORD-2024-001',
    total_amount: 15000,

    // 销售员关系数据（多对一）
    sales: {
      id: 101,
      name: '李销售',
      nick_name: '小李',
      employee_no: 'EMP001',
      department: '销售部',
      position: '高级销售',
      email: '<EMAIL>',
      phone: '13900139000',
      address: '上海市浦东新区',
      monthly_sales: 120000,
      yearly_sales: 1200000,
      customer_count: 25,
      hire_date: '2020-03-15',
      status: 'active',
    },

    // 客户关系数据（多对一）
    customer: {
      id: 201,
      customer_name: '张三公司',
      contact_person: '张总',
      industry: '制造业',
      level: 'VIP',
    },

    // 订单项关系数据（一对多）
    demo_items: [
      {
        id: 301,
        product_name: '产品A',
        quantity: 10,
        unit_price: 500,
        total: 5000,
      },
      {
        id: 302,
        product_name: '产品B',
        quantity: 20,
        unit_price: 300,
        total: 6000,
      },
      {
        id: 303,
        product_name: '产品C',
        quantity: 8,
        unit_price: 500,
        total: 4000,
      },
    ],
  },
  // ... 更多测试数据
]
```

## 测试步骤

### 1. 测试 API 动态渲染（推荐）

1. 点击"销售员"列中的链接
2. 应该打开一个大尺寸的 drawer
3. 显示加载状态（旋转图标）
4. 自动调用 `hr/employee` API 获取数据
5. 根据元数据自动生成详情界面
6. 检查字段分组是否正确（基本信息、联系信息等）
7. 检查字段类型渲染是否正确（日期、货币、状态等）
8. 测试关闭功能

### 2. 测试业务组件展示（Drawer）

1. 点击"销售员"列中的链接
2. 应该打开一个大尺寸的 drawer
3. 显示 `SalesPersonDetail` 组件内容
4. 检查数据是否正确传递
5. 检查标题是否动态生成
6. 测试关闭功能

### 2. 测试对话框展示

1. 点击"订单项"列中的徽章
2. 应该打开一个中等尺寸的 dialog
3. 显示默认的数据预览界面
4. 检查数组数据是否正确展示

### 3. 测试路由页面展示

1. 点击"客户"列中的链接
2. 应该显示页面跳转提示界面
3. 点击"在新标签页中打开"按钮
4. 检查是否正确构建了 URL
5. 点击"在当前页面打开"按钮
6. 检查路由跳转是否正常

## 预期结果

### 销售员列（API 动态渲染）

- ✅ 显示销售员昵称和员工编号
- ✅ 点击后打开 drawer
- ✅ 显示加载状态和进度
- ✅ 自动调用 API 获取完整数据
- ✅ 根据元数据自动生成界面布局
- ✅ 字段按类型分组显示（基本信息、联系信息、业务信息、系统信息）
- ✅ 不同字段类型正确渲染（文本、数字、日期、货币、状态、邮箱、电话等）
- ✅ 关系字段单独显示在关联信息区域
- ✅ 错误处理和重试功能

### 客户列（API 动态渲染 - Dialog）

- ✅ 显示客户名称
- ✅ 点击后打开 dialog
- ✅ 调用 `crm/customer` API
- ✅ 显示客户完整信息
- ✅ 界面布局美观整洁

### 销售员列（业务组件 - 备用方案）

- ✅ 显示销售员昵称和员工编号
- ✅ 点击后打开 drawer
- ✅ 显示完整的销售员信息卡片
- ✅ 包含头像、基本信息、联系信息、业绩统计
- ✅ 有编辑和查看详情按钮

### 订单项列（默认预览）

- ✅ 显示订单项数量徽章
- ✅ 点击后打开 dialog
- ✅ 显示所有订单项的列表
- ✅ 每个项目显示产品名称、数量、单价等信息

### 客户列（路由跳转）

- ✅ 显示客户名称
- ✅ 点击后显示跳转选项
- ✅ 正确构建目标 URL
- ✅ 支持新标签页和当前页面跳转

## 调试技巧

### 1. 检查控制台输出

```javascript
// 应该看到以下日志
console.log('打开销售员详情:', salesData)
console.log('关闭销售员详情')
console.log('跳转到客户详情页面:', customerData)
```

### 2. 检查组件加载

- 打开浏览器开发工具
- 查看 Network 标签
- 确认业务组件文件被正确加载

### 3. 检查数据传递

- 在业务组件中添加 `console.log(props)`
- 确认 `data` 和 `row` 属性正确传递

### 4. 检查路由构建

- 在控制台查看构建的 URL
- 确认查询参数格式正确

## 常见问题

### 1. 组件无法加载

**问题**: 点击关系列后没有反应
**解决**:

- 检查组件路径是否正确
- 确认 `SalesPersonDetail.vue` 文件存在
- 查看控制台错误信息

### 2. 数据显示异常

**问题**: 组件显示但数据为空
**解决**:

- 检查测试数据结构
- 确认关系字段名称正确
- 验证 `displayField` 配置

### 3. 样式问题

**问题**: 弹窗样式异常
**解决**:

- 检查 Tailwind CSS 是否正确加载
- 确认 shadcn-vue 组件样式正常
- 检查 z-index 层级问题

### 4. 路由跳转失败

**问题**: 点击路由跳转按钮无反应
**解决**:

- 检查目标路由是否存在
- 确认路由配置正确
- 验证 Vue Router 版本兼容性
