/**
 * 关系渲染器类型定义
 *
 * 用于定义关系字段渲染相关的配置和接口
 * 支持多对一、一对多等关系类型的渲染配置
 */

import type { BaseRendererConfig, BaseRendererProps } from '../../types'

// 关联模型映射类型
export type RelationModelMapping = Record<string, string>

export interface RelationRendererConfig extends BaseRendererConfig {
  // 基础配置
  autoFromMetadata?: boolean
  displayField?: string
  secondaryFields?: string[]
  formatter?: (data: any, row?: any) => string

  // 显示配置
  variant?: 'text' | 'badge' | 'avatar' | 'link'
  maxDisplay?: number
  overflowMode?: 'tooltip' | 'expand' | 'count'

  // 交互配置
  onClick?: (relationData: any, row: any) => void | boolean

  // 详情展示配置
  detailMode?: 'drawer' | 'dialog' | 'page'

  // 业务组件配置
  component?: string | any // 组件名称或组件对象
  componentProps?: Record<string, any> // 传递给组件的 props

  // API 动态渲染配置
  moduleModel?: string // API 模块模型名称，如 'sales/Order', 'crm/customer'
  apiDataKey?: string // 从关系数据中获取 ID 的字段名，默认为 'id'
  enableApiRendering?: boolean // 是否启用 API 动态渲染，默认 true（当配置了 moduleModel 时）

  // 路由配置
  routePath?: string | ((data: any) => string) // 路由路径
  routeQuery?: Record<string, any> | ((data: any) => Record<string, any>) // 路由查询参数

  // 弹窗配置
  title?: string | ((data: any) => string) // 弹窗标题
  drawerSize?: 'large' | 'middle' | 'small' | 'mini' // drawer 尺寸
  dialogSize?: 'large' | 'middle' | 'small' | 'mini' // dialog 尺寸

  // 事件回调
  onOpen?: (data: any) => void // 打开时回调
  onClose?: () => void // 关闭时回调

  // 错误处理
  errorText?: string
}

export interface RelationRendererProps extends BaseRendererProps {
  config?: RelationRendererConfig
}
