/**
 * 关系渲染器工具函数
 *
 * 提供关系数据处理、格式化、映射等工具函数
 */

import type { RelationRendererConfig } from './types'

/**
 * 关联模型映射表
 * 将后端的模型名称映射到对应的API模块路径
 */
export const relationModelMap: Record<string, string> = {
  Employee: 'hr/employee',
  DemoItem: 'demo/demo_item',
  // TODO: 根据实际项目需要添加更多映射
}

/**
 * 获取模块模型路径
 * @param relatedModel 关联模型名称
 * @returns API模块路径，如果未找到返回null
 */
export function getModuleModelPath(relatedModel: string): string | null {
  return relationModelMap[relatedModel] || null
}

/**
 * 推断显示字段
 * 根据关系数据的结构自动推断最适合显示的字段
 * @param relationData 关系数据
 * @returns 推断的显示字段名，如果无法推断返回null
 */
export function inferDisplayField(relationData: any): string | null {
  if (!relationData || typeof relationData !== 'object') {
    return null
  }

  // 优先级顺序：name > title > nick_name > display_name > 第一个字符串字段
  const priorityFields = ['name', 'title', 'nick_name', 'display_name']

  for (const field of priorityFields) {
    if (relationData[field] && typeof relationData[field] === 'string') {
      return field
    }
  }

  // 如果没有优先字段，查找第一个字符串字段
  for (const [key, value] of Object.entries(relationData)) {
    if (typeof value === 'string' && value.trim() !== '') {
      return key
    }
  }

  return null
}

/**
 * 格式化关系显示文本
 * @param data 关系数据（可能是单个对象或数组）
 * @param config 渲染配置
 * @returns 格式化后的显示文本
 */
export function formatRelationDisplay(
  data: any,
  config: RelationRendererConfig
): string {
  if (!data) {
    return '--'
  }

  // 如果有自定义格式化函数，优先使用
  if (config.formatter) {
    try {
      return config.formatter(data)
    } catch (error) {
      console.warn('关系渲染器自定义格式化函数执行失败:', error)
    }
  }

  // 处理数组情况（一对多）
  if (Array.isArray(data)) {
    if (data.length === 0) {
      return '--'
    }

    const maxDisplay = config.maxDisplay || 2
    const displayItems = data.slice(0, maxDisplay)

    const texts = displayItems.map((item) => getItemDisplayText(item, config))
    let result = texts.join(', ')

    if (data.length > maxDisplay) {
      const remaining = data.length - maxDisplay
      result += ` +${remaining}项`
    }

    return result
  }

  // 处理单个对象情况（多对一）
  return getItemDisplayText(data, config)
}

/**
 * 获取单个项目的显示文本
 * @param item 单个关系数据项
 * @param config 渲染配置
 * @returns 显示文本
 */
function getItemDisplayText(item: any, config: RelationRendererConfig): string {
  if (!item || typeof item !== 'object') {
    return String(item || '--')
  }

  // 使用配置的显示字段
  if (config.displayField && item[config.displayField]) {
    return String(item[config.displayField])
  }

  // 自动推断显示字段
  const inferredField = inferDisplayField(item)
  if (inferredField && item[inferredField]) {
    return String(item[inferredField])
  }

  // 如果有id，返回"未知项目 (ID: xxx)"
  if (item.id) {
    return `未知项目 (ID: ${item.id})`
  }

  return '未知项目'
}

/**
 * 获取关系类型
 * @param relationData 关系数据
 * @returns 关系类型
 */
export function getRelationType(relationData: any): 'MANYTOONE' | 'ONETOMANY' {
  return Array.isArray(relationData) ? 'ONETOMANY' : 'MANYTOONE'
}

/**
 * 构建次要信息显示文本
 * @param item 关系数据项
 * @param secondaryFields 次要字段列表
 * @returns 次要信息文本数组
 */
export function buildSecondaryInfo(
  item: any,
  secondaryFields?: string[]
): string[] {
  if (!item || !secondaryFields || secondaryFields.length === 0) {
    return []
  }

  return secondaryFields
    .map((field) => item[field])
    .filter((value) => value !== null && value !== undefined && value !== '')
    .map((value) => String(value))
}
