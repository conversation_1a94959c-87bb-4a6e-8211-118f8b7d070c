<template>
  <!-- Badge 变体 -->
  <Badge v-if="variant === 'badge'" :class="badgeClasses">
    <Icon v-if="status.icon" :icon="status.icon" class="mr-1 inline-block" />
    {{ status.text }}
  </Badge>

  <!-- Dot 变体 -->
  <div v-else-if="variant === 'dot'" :class="dotContainerClasses">
    <div :class="dotClasses"></div>
    <span :class="dotTextClasses">{{ status.text }}</span>
  </div>

  <!-- Text 变体 -->
  <span v-else-if="variant === 'text'" :class="textClasses">
    <Icon v-if="status.icon" :icon="status.icon" class="mr-1 inline-block" />
    {{ status.text }}
  </span>

  <!-- Progress 变体 -->
  <div v-else-if="variant === 'progress'" :class="progressContainerClasses">
    <div class="w-full">
      <div class="flex items-center justify-between mb-1">
        <span :class="progressTextClasses">{{ status.text }}</span>
        <span
          v-if="progressPercentage !== undefined"
          class="text-xs text-gray-500"
        >
          {{ progressPercentage }}%
        </span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div
          :class="progressBarClasses"
          :style="{ width: `${progressPercentage || 0}%` }"
        ></div>
      </div>
    </div>
  </div>

  <!-- 默认回退到 Badge -->
  <Badge v-else :class="badgeClasses">
    <Icon v-if="status.icon" :icon="status.icon" class="mr-1 inline-block" />
    {{ status.text }}
  </Badge>
</template>

<script setup lang="ts">
defineOptions({
  name: 'StatusRenderer',
})

import { Badge } from '@/components/ui/badge'
import { Icon } from '@iconify/vue'
import type { StatusRendererProps } from './types'
import { useStatusRenderer } from './useRenderer'

const props = defineProps<StatusRendererProps>()

const {
  variant,
  status,
  progressPercentage,
  badgeClasses,
  dotContainerClasses,
  dotClasses,
  dotTextClasses,
  textClasses,
  progressContainerClasses,
  progressTextClasses,
  progressBarClasses,
} = useStatusRenderer(props)
</script>
