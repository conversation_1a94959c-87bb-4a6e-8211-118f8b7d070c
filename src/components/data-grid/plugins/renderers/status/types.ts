import { BaseRendererConfig, BaseRendererProps } from '../../types'

/**
 * 状态映射配置
 */
export interface StatusMap {
  [key: string | number]: {
    text: string
    type?: string
    icon?: string
    progress?: number
  }
}

/**
 * 状态渲染器配置接口
 */
export interface StatusRendererConfig extends BaseRendererConfig {
  /** 状态映射 */
  statusMap?: StatusMap
  /** 默认状态 */
  defaultStatus?: {
    text: string
    type?: string
    icon?: string
    progress?: number
  }
  /** 变体类型 */
  variant?: 'badge' | 'dot' | 'text' | 'progress'
  /** 是否从元数据自动生成 */
  autoFromMetadata?: boolean
  /** 进度百分比 */
  progress?: number
}

/**
 * 状态渲染器属性接口
 */
export interface StatusRendererProps extends BaseRendererProps {
  config?: StatusRendererConfig
}
