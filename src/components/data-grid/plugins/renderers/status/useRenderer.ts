/**
 * 状态渲染器逻辑钩子
 */

import { computed } from 'vue'
import { cn } from '@/lib/utils'
import {
  createThemeClassBuilder,
  getThemeColors,
} from '../../utils/rendererUtils'
import type { StatusRendererProps } from './types'

export const useStatusRenderer = (props: StatusRendererProps) => {
  // 获取状态值 - 保持原有逻辑
  const value = computed(() => {
    return props.field ? props.row?.[props.field] : props.value
  })

  // 获取状态配置（现在由 DataGrid 预处理）
  const statusMap = computed(() => props.config?.statusMap || {})

  const defaultStatus = computed(
    () => props.config?.defaultStatus || { text: 'Unknown', type: 'default' }
  )

  // 获取渲染变体
  const variant = computed(() => props.config?.variant || 'badge')

  // 获取当前状态
  const status = computed(() => {
    const currentValue = value.value
    if (statusMap.value[currentValue]) {
      return statusMap.value[currentValue]
    }
    return defaultStatus.value
  })

  // 获取进度百分比（仅用于 progress 变体）
  const progressPercentage = computed(() => {
    return status.value.progress || props.config?.progress
  })

  // 使用统一的样式构建器 - 遵循DRY原则，移除重复的颜色配置
  const styleBuilder = computed(() => {
    const statusType = status.value.type || 'default'
    return createThemeClassBuilder(statusType)
  })

  // 简化的样式类 - 移除重复代码，使用统一的样式系统
  const badgeClasses = computed(() => {
    return styleBuilder.value.badge()
  })

  const dotContainerClasses = computed(() => {
    return styleBuilder.value.dotContainer()
  })

  const dotClasses = computed(() => {
    return styleBuilder.value.dot()
  })

  const dotTextClasses = computed(() => {
    return styleBuilder.value.text()
  })

  const textClasses = computed(() => {
    return cn('', styleBuilder.value.text())
  })

  const progressContainerClasses = computed(() => {
    return cn('w-full')
  })

  const progressTextClasses = computed(() => {
    return styleBuilder.value.text()
  })

  const progressBarClasses = computed(() => {
    const colors = getThemeColors(status.value.type || 'default')
    return cn('h-2 rounded-full transition-all duration-300', colors.dot)
  })

  return {
    value,
    variant,
    status,
    progressPercentage,
    badgeClasses,
    dotContainerClasses,
    dotClasses,
    dotTextClasses,
    textClasses,
    progressContainerClasses,
    progressTextClasses,
    progressBarClasses,
  }
}
