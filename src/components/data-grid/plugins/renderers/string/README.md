# String Renderer 字符串渲染器

字符串字段的通用渲染器，支持多种显示变体和文本处理功能。

## 功能特性

- **多种显示变体**：文本、徽章、代码块、截断显示
- **文本处理**：大小写转换、前缀后缀、截断处理
- **样式定制**：对齐方式、字体样式、主题色彩
- **交互功能**：一键复制到剪贴板
- **响应式设计**：适配不同屏幕尺寸

## 使用方式

### 基础用法

```vue
<template>
  <StringRenderer :value="text" :row="rowData" field="description" />
</template>
```

### 配置选项

```typescript
interface StringRendererConfig {
  variant?: 'text' | 'badge' | 'code' | 'truncated'
  maxLength?: number
  ellipsis?: string
  align?: 'left' | 'center' | 'right'
  fontStyle?: 'normal' | 'bold' | 'italic'
  theme?: 'default' | 'primary' | 'success' | 'warning' | 'danger' | 'muted'
  copyable?: boolean
  prefix?: string
  suffix?: string
  transform?: 'uppercase' | 'lowercase' | 'capitalize' | 'none'
}
```

### 使用示例

#### 1. 文本变体（默认）

```vue
<StringRenderer 
  :value="username" 
  :config="{ 
    align: 'center', 
    fontStyle: 'bold',
    theme: 'primary' 
  }" 
/>
```

#### 2. 徽章变体

```vue
<StringRenderer 
  :value="status" 
  :config="{ 
    variant: 'badge',
    theme: 'success',
    transform: 'uppercase' 
  }" 
/>
```

#### 3. 代码变体

```vue
<StringRenderer 
  :value="code" 
  :config="{ 
    variant: 'code',
    copyable: true 
  }" 
/>
```

#### 4. 截断变体

```vue
<StringRenderer 
  :value="longDescription" 
  :config="{ 
    variant: 'truncated',
    maxLength: 30,
    copyable: true 
  }" 
/>
```

#### 5. 前缀后缀

```vue
<StringRenderer 
  :value="amount" 
  :config="{ 
    prefix: '¥',
    suffix: ' 元',
    align: 'right',
    fontStyle: 'bold' 
  }" 
/>
```

## 配置参数详解

| 参数 | 类型 | 默认值 | 描述 |
|------|------|-------|------|
| `variant` | `'text' \| 'badge' \| 'code' \| 'truncated'` | `'text'` | 显示变体 |
| `maxLength` | `number` | `50` | 最大显示长度（truncated 变体） |
| `ellipsis` | `string` | `'...'` | 截断省略符 |
| `align` | `'left' \| 'center' \| 'right'` | `'left'` | 文本对齐方式 |
| `fontStyle` | `'normal' \| 'bold' \| 'italic'` | `'normal'` | 字体样式 |
| `theme` | `'default' \| 'primary' \| 'success' \| 'warning' \| 'danger' \| 'muted'` | `'default'` | 颜色主题 |
| `copyable` | `boolean` | `false` | 是否可复制 |
| `prefix` | `string` | - | 前缀文本 |
| `suffix` | `string` | - | 后缀文本 |
| `transform` | `'uppercase' \| 'lowercase' \| 'capitalize' \| 'none'` | `'none'` | 文本转换 |

## 样式主题

支持以下颜色主题：

- `default` - 默认灰色
- `primary` - 主要蓝色
- `success` - 成功绿色
- `warning` - 警告橙色
- `danger` - 危险红色
- `muted` - 静音灰色

## 注意事项

1. **复制功能**：需要浏览器支持 `navigator.clipboard` API
2. **截断显示**：鼠标悬停时会显示完整内容的 tooltip
3. **性能优化**：使用 computed 属性缓存计算结果
4. **响应式**：样式类基于 Tailwind CSS，支持响应式设计

---

# ColumnHelper 使用方法

## 概述

`columnHelper` 提供了 6 个便捷的字符串渲染器方法，可以快速创建不同类型的字符串列配置。

## 可用方法

### 1. `string()` - 基础字符串列

创建基本的字符串显示列。

```typescript
columnHelper.string(field: string, title: string, config?: StringRendererConfig)
```

**示例：**
```typescript
// 基础文本列
columnHelper.string('customer_name', '客户名称')

// 带复制功能的文本列
columnHelper.string('customer_name', '客户名称', {
  copyable: true,
  fontStyle: 'bold'
})

// 居中对齐的主要色彩文本
columnHelper.string('title', '标题', {
  align: 'center',
  theme: 'primary',
  fontStyle: 'bold'
})
```

### 2. `textBadge()` - 文本徽章列

创建徽章样式的文本列，适合状态标签等场景。

```typescript
columnHelper.textBadge(field: string, title: string, config?: StringRendererConfig)
```

**示例：**
```typescript
// 成功色彩的徽章
columnHelper.textBadge('category', '分类', {
  theme: 'success'
})

// 自定义主题徽章
columnHelper.textBadge('priority', '优先级', {
  theme: 'warning',
  transform: 'uppercase'
})

// 带前缀的徽章
columnHelper.textBadge('level', '等级', {
  prefix: 'LV.',
  theme: 'primary'
})
```

### 3. `code()` - 代码列

创建代码块样式的列，默认支持复制功能。

```typescript
columnHelper.code(field: string, title: string, config?: StringRendererConfig)
```

**示例：**
```typescript
// 基础代码列
columnHelper.code('customer_code', '客户编码')

// 自定义样式的代码列
columnHelper.code('api_key', 'API Key', {
  theme: 'muted',
  align: 'center'
})

// 带前缀的代码列
columnHelper.code('order_no', '订单号', {
  prefix: '#',
  copyable: true
})
```

### 4. `truncatedText()` - 截断文本列

创建截断显示的文本列，超长文本自动截断，支持悬停查看完整内容。

```typescript
columnHelper.truncatedText(field: string, title: string, maxLength: number = 30, config?: StringRendererConfig)
```

**示例：**
```typescript
// 基础截断文本
columnHelper.truncatedText('address', '地址', 25)

// 可复制的截断文本
columnHelper.truncatedText('description', '描述', 50, {
  copyable: true,
  theme: 'muted'
})

// 自定义省略符的截断文本
columnHelper.truncatedText('content', '内容', 40, {
  ellipsis: '…',
  copyable: true
})
```

### 5. `title()` - 标题列

创建标题样式的列，自动首字母大写，粗体显示。

```typescript
columnHelper.title(field: string, title: string, config?: StringRendererConfig)
```

**示例：**
```typescript
// 基础标题列
columnHelper.title('name', '名称')

// 居中对齐的标题
columnHelper.title('project_name', '项目名称', {
  align: 'center'
})

// 自定义主题的标题
columnHelper.title('department', '部门', {
  theme: 'success',
  copyable: true
})
```

### 6. `description()` - 描述列

创建描述文本列，默认截断50字符，支持复制，使用静音色彩。

```typescript
columnHelper.description(field: string, title: string, config?: StringRendererConfig)
```

**示例：**
```typescript
// 基础描述列
columnHelper.description('notes', '备注')

// 自定义截断长度的描述
columnHelper.description('summary', '摘要', {
  maxLength: 80
})

// 不截断的描述列
columnHelper.description('remark', '说明', {
  variant: 'text',
  maxLength: 0  // 不截断
})
```

## 完整使用示例

### 在 DataGrid 中使用

```vue
<script setup lang="ts">
import { useDataGrid } from '@/components/data-grid/composables/useDataGrid'
import { onMounted } from 'vue'

const dataGridInstance = useDataGrid('demo/demo', {
  enableSelection: 'checkbox',
  columns: [],
})

const columnHelper = dataGridInstance.getColumnHelper()

onMounted(() => {
  const columns = [
    // 基础文本列
    columnHelper.string('customer_name', '客户名称', {
      fontStyle: 'bold',
      copyable: true
    }),

    // 徽章状态列
    columnHelper.textBadge('status', '状态', {
      theme: 'success',
      transform: 'uppercase'
    }),

    // 代码列
    columnHelper.code('order_no', '订单号', {
      prefix: '#'
    }),

    // 截断文本列
    columnHelper.truncatedText('address', '地址', 30, {
      copyable: true
    }),

    // 标题列
    columnHelper.title('project_name', '项目名称'),

    // 描述列
    columnHelper.description('notes', '备注', {
      maxLength: 60
    })
  ]

  dataGridInstance.gridOptions.value.columns = columns
})
</script>
```

### 高级配置示例

```typescript
// 货币显示列
columnHelper.string('amount', '金额', {
  prefix: '¥',
  suffix: ' 元',
  align: 'right',
  fontStyle: 'bold',
  theme: 'success'
})

// 链接样式文本
columnHelper.string('website', '官网', {
  theme: 'primary',
  copyable: true,
  transform: 'lowercase'
})

// 多语言标题
columnHelper.title('product_name', '产品名称', {
  transform: 'capitalize',
  copyable: true,
  theme: 'primary'
})

// 长描述文本
columnHelper.description('full_description', '详细描述', {
  maxLength: 100,
  ellipsis: '... 点击查看更多'
})
```

## 配置参数复用

可以预定义常用配置，提高代码复用性：

```typescript
// 预定义配置
const copyableTextConfig = {
  copyable: true,
  theme: 'default'
}

const primaryTitleConfig = {
  theme: 'primary',
  fontStyle: 'bold',
  copyable: true
}

const compactDescConfig = {
  maxLength: 25,
  copyable: true,
  theme: 'muted'
}

// 使用预定义配置
const columns = [
  columnHelper.string('name', '名称', primaryTitleConfig),
  columnHelper.code('code', '编码', copyableTextConfig),
  columnHelper.description('desc', '描述', compactDescConfig)
]
```

## 注意事项

1. **字段映射**：确保 `field` 参数与数据源中的字段名对应
2. **宽度设置**：可通过 `width` 参数设置列宽，默认宽度为 150px
3. **主题一致性**：建议在同一表格中保持主题色彩的一致性
4. **性能考虑**：大量数据时建议适当控制截断长度和复制功能的使用
5. **可访问性**：截断文本会自动添加 `title` 属性，便于无障碍访问

## 与其他渲染器配合使用

String 渲染器可以与其他渲染器配合使用：

```typescript
const columns = [
  columnHelper.string('name', '名称', { fontStyle: 'bold' }),
  columnHelper.status('status', '状态'),
  columnHelper.date('created_at', '创建时间'),
  columnHelper.actions('操作', { 
    actions: [
      { text: '编辑', onClick: (row) => console.log('编辑', row) }
    ]
  })
]
```