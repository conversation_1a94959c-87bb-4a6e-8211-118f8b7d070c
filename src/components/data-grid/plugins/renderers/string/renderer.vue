<template>
  <div class="inline-flex items-center">
    <!-- Badge 变体 -->
    <Badge v-if="variant === 'badge'" :class="badgeClasses" @click="copyToClipboard">
      {{ displayValue }}
    </Badge>

    <!-- Code 变体 -->
    <code v-else-if="variant === 'code'" :class="codeClasses" @click="copyToClipboard">
      {{ displayValue }}
    </code>

    <!-- Truncated 变体 -->
    <span 
      v-else-if="variant === 'truncated'" 
      :class="truncatedClasses" 
      :title="transformedValue"
      @click="copyToClipboard"
    >
      {{ displayValue }}
    </span>

    <!-- 默认 Text 变体 -->
    <span v-else :class="textClasses" @click="copyToClipboard">
      {{ displayValue }}
    </span>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'StringRenderer',
})

import { Badge } from '@/components/ui/badge'
import type { StringRendererProps } from './types'
import { useStringRenderer } from './useRenderer'

const props = defineProps<StringRendererProps>()

const {
  displayValue,
  transformedValue,
  variant,
  copyable,
  textClasses,
  badgeClasses,
  codeClasses,
  truncatedClasses,
  copyToClipboard,
} = useStringRenderer(props)
</script>