import { BaseRendererConfig, BaseRendererProps } from '../../types'

/**
 * 字符串渲染器配置接口
 */
export interface StringRendererConfig extends BaseRendererConfig {
  /** 显示变体 */
  variant?: 'text' | 'badge' | 'code' | 'truncated'
  /** 最大显示长度（truncated 变体使用） */
  maxLength?: number
  /** 截断省略符 */
  ellipsis?: string
  /** 文本对齐方式 */
  align?: 'left' | 'center' | 'right'
  /** 字体样式 */
  fontStyle?: 'normal' | 'bold' | 'italic'
  /** 文本颜色主题 */
  theme?: 'default' | 'primary' | 'success' | 'warning' | 'danger' | 'muted'
  /** 是否可复制 */
  copyable?: boolean
  /** 前缀 */
  prefix?: string
  /** 后缀 */
  suffix?: string
  /** 转换函数 */
  transform?: 'uppercase' | 'lowercase' | 'capitalize' | 'none'
}

/**
 * 字符串渲染器属性接口
 */
export interface StringRendererProps extends BaseRendererProps {
  config?: StringRendererConfig
}