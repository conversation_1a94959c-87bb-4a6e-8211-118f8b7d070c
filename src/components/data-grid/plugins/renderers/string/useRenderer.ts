/**
 * 字符串渲染器逻辑钩子
 */

import { computed } from 'vue'
import { cn } from '@/lib/utils'
import { createThemeClassBuilder } from '../../utils/rendererUtils'
import type { StringRendererProps } from './types'

export const useStringRenderer = (props: StringRendererProps) => {
  // 获取字符串值
  const value = computed(() => {
    const rawValue = props.field ? props.row?.[props.field] : props.value
    return rawValue?.toString() || ''
  })

  // 配置
  const config = computed(() => props.config || {})
  const variant = computed(() => config.value.variant || 'text')
  const maxLength = computed(() => config.value.maxLength || 50)
  const ellipsis = computed(() => config.value.ellipsis || '...')
  const align = computed(() => config.value.align || 'left')
  const fontStyle = computed(() => config.value.fontStyle || 'normal')
  const theme = computed(() => config.value.theme || 'default')
  const copyable = computed(() => config.value.copyable || false)
  const transform = computed(() => config.value.transform || 'none')

  // 处理文本转换
  const transformedValue = computed(() => {
    const val = value.value
    switch (transform.value) {
      case 'uppercase':
        return val.toUpperCase()
      case 'lowercase':
        return val.toLowerCase()
      case 'capitalize':
        return val.charAt(0).toUpperCase() + val.slice(1).toLowerCase()
      default:
        return val
    }
  })

  // 处理截断
  const displayValue = computed(() => {
    let text = transformedValue.value
    
    // 添加前缀
    if (config.value.prefix) {
      text = config.value.prefix + text
    }
    
    // 添加后缀
    if (config.value.suffix) {
      text = text + config.value.suffix
    }

    // 截断处理
    if (variant.value === 'truncated' && text.length > maxLength.value) {
      return text.slice(0, maxLength.value) + ellipsis.value
    }

    return text
  })

  // 获取样式构建器
  const styleBuilder = computed(() => {
    return createThemeClassBuilder(theme.value)
  })

  // 文本对齐样式
  const alignmentClass = computed(() => {
    switch (align.value) {
      case 'center':
        return 'text-center'
      case 'right':
        return 'text-right'
      default:
        return 'text-left'
    }
  })

  // 字体样式
  const fontClass = computed(() => {
    switch (fontStyle.value) {
      case 'bold':
        return 'font-semibold'
      case 'italic':
        return 'italic'
      default:
        return 'font-normal'
    }
  })

  // 文本样式类
  const textClasses = computed(() => {
    return cn(
      alignmentClass.value,
      fontClass.value,
      styleBuilder.value.text()
    )
  })

  // Badge 样式类
  const badgeClasses = computed(() => {
    return cn(
      'inline-flex items-center',
      styleBuilder.value.badge()
    )
  })

  // Code 样式类
  const codeClasses = computed(() => {
    return cn(
      'font-mono text-sm px-1 py-0.5 rounded bg-muted',
      alignmentClass.value,
      styleBuilder.value.text()
    )
  })

  // 截断样式类
  const truncatedClasses = computed(() => {
    return cn(
      'truncate',
      alignmentClass.value,
      fontClass.value,
      styleBuilder.value.text(),
      copyable.value && 'cursor-pointer hover:bg-muted/50'
    )
  })

  // 复制功能
  const copyToClipboard = async () => {
    if (!copyable.value) return
    
    try {
      await navigator.clipboard.writeText(transformedValue.value)
      // 可以在这里添加提示消息
    } catch (err) {
      console.warn('Failed to copy text:', err)
    }
  }

  return {
    value,
    displayValue,
    transformedValue,
    variant,
    copyable,
    textClasses,
    badgeClasses,
    codeClasses,
    truncatedClasses,
    copyToClipboard,
  }
}