# User Renderer Plugin

数据网格用户ID转姓名渲染器插件，支持高性能缓存和多种显示样式。

## 功能特性

### 核心功能
- **用户ID转姓名**：自动将用户ID转换为用户名显示
- **智能缓存**：集成 `userCache.ts` 缓存机制，避免重复API调用
- **加载状态**：显示加载中、错误、空数据状态
- **多种变体**：支持文本、徽章、头像、链接四种显示样式

### 性能优化
- **全局缓存**：组件间共享用户数据缓存
- **防重复请求**：同一用户ID只发起一次API请求
- **错误状态管理**：错误用户ID缓存，避免反复请求
- **组件实例计数**：优化批量加载性能

## 使用方法

### 基本用法

```typescript
import { getPluginManager } from '@/components/data-grid/plugins'

const pluginManager = getPluginManager()
const helper = pluginManager.getColumnHelper()

// 基础用户列
const userColumn = helper.user('create_by', '创建者')

// 用户头像列  
const avatarColumn = helper.userAvatar('author_id', '作者', {
  avatarSize: 'lg',
  showTooltip: true
})

// 用户徽章列
const badgeColumn = helper.userBadge('assignee_id', '负责人')

// 用户链接列
const linkColumn = helper.userLink('reviewer_id', '审核人', {
  onClick: (user, row) => {
    console.log('点击用户:', user.username)
    // 可以跳转到用户详情页等操作
  }
})
```

### 高级配置

```typescript
const advancedColumn = helper.user('user_id', '用户', {
  variant: 'avatar',
  displayFormat: 'username',
  showTooltip: true,
  tooltipContent: 'userinfo',
  avatarSize: 'md',
  avatarFallback: 'initials',
  loadingText: '正在加载...',
  errorText: '加载用户失败',
  nullText: '未分配',
  onClick: (user, row) => {
    // 自定义点击处理
    navigateToUserProfile(user.id)
    return false // 阻止默认行为
  },
  customFormatter: (user) => `${user.username} (ID: ${user.id})`,
  customTooltipFormatter: (user) => `
用户ID: ${user.id}
用户名: ${user.username}
最后活跃: ${formatDate(user.last_login)}
  `
})
```

## 配置选项

### UserRendererConfig

```typescript
interface UserRendererConfig {
  // 显示配置
  variant?: 'text' | 'badge' | 'avatar' | 'link'
  displayFormat?: 'username' | 'fullname' | 'custom'
  customFormatter?: (user: AllUsersName) => string
  
  // 状态文本
  loadingText?: string    // 默认: '加载中...'
  errorText?: string      // 默认: '加载失败'
  nullText?: string       // 默认: '--'
  
  // 交互配置
  onClick?: (user: AllUsersName, row: any) => void | boolean
  showTooltip?: boolean
  tooltipContent?: 'username' | 'userinfo' | 'custom'
  customTooltipFormatter?: (user: AllUsersName) => string
  
  // 头像配置
  showAvatar?: boolean
  avatarSize?: 'sm' | 'md' | 'lg'
  avatarFallback?: 'username' | 'initials'
  
  // 缓存配置
  enableCache?: boolean   // 默认: true
  cacheRefresh?: boolean  // 默认: false
}
```

### 显示变体

#### 1. text (默认)
```typescript
helper.user('user_id', '用户', {
  variant: 'text',
  showTooltip: true
})
```

#### 2. badge (徽章)
```typescript
helper.userBadge('user_id', '用户', {
  showTooltip: true,
  tooltipContent: 'userinfo'
})
```

#### 3. avatar (头像)
```typescript
helper.userAvatar('user_id', '用户', {
  avatarSize: 'lg',
  avatarFallback: 'initials',
  showTooltip: true
})
```

#### 4. link (链接)
```typescript
helper.userLink('user_id', '用户', {
  onClick: (user, row) => {
    router.push(`/users/${user.id}`)
  }
})
```

## 状态管理

### 显示状态
- **loading**: 显示加载中状态（带动画）
- **error**: 显示错误状态（红色文本）
- **empty**: 显示空数据状态（灰色文本）
- **success**: 显示用户数据

### 缓存机制
- 使用全局 `userCache.ts` 实例
- 30分钟缓存过期时间
- 5分钟强制刷新时间
- 支持手动刷新缓存

## 性能特性

### 全局状态管理
```typescript
const globalUserState = {
  cache: new Map<number, AllUsersName>(),
  loadingUsers: new Set<number>(),
  errorUsers: new Set<number>(),
  lastCacheUpdate: 0,
}
```

### 防重复请求
- 同一用户ID只发起一次API请求
- 多个组件实例共享加载状态
- 错误状态缓存避免反复请求

### 组件实例优化
- 组件实例计数器
- 批量加载优化
- 内存泄漏防护

## 样式定制

### CSS 变量
```css
.user-renderer {
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
}

/* 加载动画 */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
```

### 头像尺寸
- `sm`: 20px (w-5 h-5)
- `md`: 24px (w-6 h-6) - 默认
- `lg`: 32px (w-8 h-8)

## 最佳实践

### 1. 性能优化
```typescript
// 启用缓存（默认开启）
const column = helper.user('user_id', '用户', {
  enableCache: true
})

// 减少不必要的tooltip
const column = helper.user('user_id', '用户', {
  showTooltip: false // 当不需要详细信息时
})
```

### 2. 用户体验
```typescript
// 提供有意义的状态文本
const column = helper.user('assignee_id', '负责人', {
  loadingText: '正在获取负责人...',
  errorText: '获取失败',
  nullText: '未分配'
})

// 自定义点击行为
const column = helper.userLink('reviewer_id', '审核人', {
  onClick: (user, row) => {
    if (user.id === currentUserId) {
      return false // 不允许点击自己
    }
    showUserProfile(user)
  }
})
```

### 3. 数据展示
```typescript
// 自定义显示格式
const column = helper.user('user_id', '用户', {
  displayFormat: 'custom',
  customFormatter: (user) => `${user.username} (${user.dept})`,
  customTooltipFormatter: (user) => `
姓名: ${user.username}
部门: ${user.dept || '未知'}
邮箱: ${user.email || '未设置'}
  `
})
```

## 故障排除

### 常见问题

1. **用户数据不显示**
   - 检查 `userCache.ts` 是否正确导入
   - 确认API接口 `/api/sys/user` 可访问
   - 查看浏览器控制台错误信息

2. **缓存不生效**
   - 确认 `enableCache: true`
   - 检查全局配置存储是否正常
   - 验证组件实例是否正确创建

3. **样式显示异常**
   - 检查 Tailwind CSS 类是否可用
   - 确认 UI 组件库导入正确
   - 验证 CSS 作用域配置

### 调试技巧

```typescript
// 启用详细日志
const column = helper.user('user_id', '用户', {
  onClick: (user, row) => {
    console.log('用户数据:', user)
    console.log('行数据:', row)
    console.log('缓存状态:', userCache.getCacheStats())
  }
})
```

## 兼容性

- Vue 3.x
- TypeScript 4.x+
- Tailwind CSS 3.x
- Shadcn Vue UI 组件库
- 现有数据网格插件系统