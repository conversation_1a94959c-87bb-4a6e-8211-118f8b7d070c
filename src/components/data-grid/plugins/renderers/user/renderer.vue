<template>
  <div class="user-renderer">
    <!-- 加载状态 -->
    <template v-if="isLoading">
      <div v-if="finalConfig.variant === 'badge'" class="inline-flex items-center">
        <Badge variant="outline" class="animate-pulse">
          <div class="w-2 h-2 bg-gray-300 rounded-full mr-1"></div>
          {{ displayText }}
        </Badge>
      </div>
      <div v-else-if="finalConfig.variant === 'avatar'" class="flex items-center animate-pulse">
        <div class="rounded-full bg-gray-200" :class="avatarSizeClasses"></div>
        <span class="ml-2 text-gray-400">{{ displayText }}</span>
      </div>
      <span v-else class="text-gray-400 animate-pulse">{{ displayText }}</span>
    </template>

    <!-- 错误状态 -->
    <template v-else-if="hasError">
      <div v-if="finalConfig.variant === 'badge'" class="inline-flex items-center">
        <Badge variant="destructive" class="cursor-help" title="加载失败">
          {{ displayText }}
        </Badge>
      </div>
      <span v-else class="text-red-500 cursor-help" title="加载失败">
        {{ displayText }}
      </span>
    </template>

    <!-- 空数据状态 -->
    <template v-else-if="!hasData">
      <span class="text-gray-400">{{ displayText }}</span>
    </template>

    <!-- 正常显示状态 -->
    <template v-else>
      <!-- Badge 变体 -->
      <Tooltip v-if="finalConfig.variant === 'badge'" :disabled="!finalConfig.showTooltip">
        <TooltipTrigger asChild>
          <Badge
            variant="secondary"
            :class="variantClasses"
            @click="handleClick"
          >
            {{ displayText }}
          </Badge>
        </TooltipTrigger>
        <TooltipContent v-if="finalConfig.showTooltip">
          <div class="whitespace-pre-line">{{ tooltipText }}</div>
        </TooltipContent>
      </Tooltip>

      <!-- Avatar 变体 -->
      <Tooltip v-else-if="finalConfig.variant === 'avatar'" :disabled="!finalConfig.showTooltip">
        <TooltipTrigger asChild>
          <div :class="variantClasses" @click="handleClick">
            <Avatar :class="avatarSizeClasses">
              <AvatarFallback class="text-xs font-medium">
                {{ avatarText }}
              </AvatarFallback>
            </Avatar>
            <span class="ml-2">{{ displayText }}</span>
          </div>
        </TooltipTrigger>
        <TooltipContent v-if="finalConfig.showTooltip">
          <div class="whitespace-pre-line">{{ tooltipText }}</div>
        </TooltipContent>
      </Tooltip>

      <!-- Link 变体 -->
      <Tooltip v-else-if="finalConfig.variant === 'link'" :disabled="!finalConfig.showTooltip">
        <TooltipTrigger asChild>
          <span :class="variantClasses" @click="handleClick">
            {{ displayText }}
          </span>
        </TooltipTrigger>
        <TooltipContent v-if="finalConfig.showTooltip">
          <div class="whitespace-pre-line">{{ tooltipText }}</div>
        </TooltipContent>
      </Tooltip>

      <!-- Text 变体（默认） -->
      <Tooltip v-else :disabled="!finalConfig.showTooltip">
        <TooltipTrigger asChild>
          <span 
            :class="variantClasses"
            @click="finalConfig.onClick ? handleClick : undefined"
            :style="{ cursor: finalConfig.onClick ? 'pointer' : 'default' }"
          >
            {{ displayText }}
          </span>
        </TooltipTrigger>
        <TooltipContent v-if="finalConfig.showTooltip">
          <div class="whitespace-pre-line">{{ tooltipText }}</div>
        </TooltipContent>
      </Tooltip>
    </template>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'UserRenderer',
})

import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import type { UserRendererProps } from './types'
import { useUserRenderer } from './useRenderer'

const props = defineProps<UserRendererProps>()

const {
  isLoading,
  hasError,
  hasData,
  finalConfig,
  displayText,
  tooltipText,
  avatarText,
  variantClasses,
  avatarSizeClasses,
  handleClick,
} = useUserRenderer(props)
</script>

<style scoped>
.user-renderer {
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
}

/* 动画效果 */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
</style>