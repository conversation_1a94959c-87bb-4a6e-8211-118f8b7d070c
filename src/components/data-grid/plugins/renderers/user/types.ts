/**
 * 用户渲染器类型定义
 *
 * 用于定义用户ID转姓名渲染相关的配置和接口
 * 支持用户数据的缓存、加载状态展示等功能
 */

import type { BaseRendererConfig, BaseRendererProps } from '../../types'
import type { AllUsersName } from '@/utils/userCache'

export interface UserRendererConfig extends BaseRendererConfig {
  // 显示配置
  variant?: 'text' | 'badge' | 'avatar' | 'link'
  
  // 格式化配置
  displayFormat?: 'username' | 'fullname' | 'custom'
  customFormatter?: (user: AllUsersName) => string
  
  // 加载状态配置
  loadingText?: string
  errorText?: string
  nullText?: string
  
  // 交互配置
  onClick?: (user: AllUsersName, row: any) => void | boolean
  showTooltip?: boolean
  tooltipContent?: 'username' | 'userinfo' | 'custom'
  customTooltipFormatter?: (user: AllUsersName) => string
  
  // 头像配置
  showAvatar?: boolean
  avatarSize?: 'sm' | 'md' | 'lg'
  avatarFallback?: 'username' | 'initials'
  
  // 缓存配置
  enableCache?: boolean
  cacheRefresh?: boolean
}

export interface UserRendererProps extends BaseRendererProps {
  config?: UserRendererConfig
}

// 用户显示状态类型
export type UserDisplayState = 
  | { type: 'loading' }
  | { type: 'error'; message?: string }
  | { type: 'empty' }
  | { type: 'success'; user: AllUsersName }