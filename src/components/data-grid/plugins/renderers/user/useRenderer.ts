/**
 * 用户渲染器组合式函数
 * 提供用户ID转姓名的核心逻辑，支持多种显示格式
 */

import { ref, computed, watch, onBeforeUnmount } from 'vue'
import userCache, { type AllUsersName } from '@/utils/userCache'
import type { UserRendererProps, UserRendererConfig } from './types'

export function useUserRenderer(props: UserRendererProps) {
  const defaultConfig: Required<UserRendererConfig> = {
    width: 120,
    variant: 'text',
    displayFormat: 'username',
    customFormatter: (user: AllUsersName) => user.username,
    loadingText: '加载中...',
    errorText: '加载失败',
    nullText: '--',
    onClick: undefined,
    showTooltip: false,
    tooltipContent: 'username',
    customTooltipFormatter: (user: AllUsersName) => user.username,
    showAvatar: false,
    avatarSize: 'md',
    avatarFallback: 'initials',
    enableCache: true,
    cacheRefresh: false,
  }

  const finalConfig = computed(() => ({
    ...defaultConfig,
    ...props.config,
  }))

  const userId = computed(() => {
    const value = props.value
    if (!value) return null
    
    const id = Number(value)
    return isNaN(id) ? null : id
  })

  // 状态管理
  const isLoading = ref(false)
  const hasError = ref(false)
  const userData = ref<AllUsersName | null>(null)
  const errorMessage = ref('')
  const isMounted = ref(true)

  const hasData = computed(() => userData.value !== null)

  // 数据加载
  const loadUserData = async (id: number) => {
    // 检查组件是否已卸载
    if (!isMounted.value) return

    isLoading.value = true
    hasError.value = false
    userData.value = null
    errorMessage.value = ''

    try {
      const user = await userCache.getUserInfo(id)
      
      // 再次检查组件是否已卸载
      if (!isMounted.value) return
      
      userData.value = user || null
    } catch (error) {
      // 检查组件是否已卸载
      if (!isMounted.value) return
      
      hasError.value = true
      errorMessage.value = String(error)
    } finally {
      // 检查组件是否已卸载
      if (isMounted.value) {
        isLoading.value = false
      }
    }
  }

  // 监听用户ID变化
  const stopWatcher = watch(
    userId,
    async (newId) => {
      if (!isMounted.value) return

      if (newId === null) {
        if (isMounted.value) {
          isLoading.value = false
          hasError.value = false
          userData.value = null
        }
        return
      }
      
      await loadUserData(newId)
    },
    { immediate: true }
  )

  // 组件卸载清理
  onBeforeUnmount(() => {
    isMounted.value = false
    stopWatcher()
    
    // 清理状态
    isLoading.value = false
    hasError.value = false
    userData.value = null
    errorMessage.value = ''
  })

  // 显示内容计算
  const displayText = computed(() => {
    if (isLoading.value) {
      return finalConfig.value.loadingText
    }
    
    if (hasError.value) {
      return finalConfig.value.errorText
    }
    
    if (!userData.value) {
      return finalConfig.value.nullText
    }
    
    return getDisplayText(userData.value)
  })

  const getDisplayText = (user: AllUsersName): string => {
    const format = finalConfig.value.displayFormat
    
    switch (format) {
      case 'username':
        return user.username
      case 'fullname':
        return user.username
      case 'custom':
        return finalConfig.value.customFormatter(user)
      default:
        return user.username
    }
  }

  const tooltipText = computed(() => {
    if (!finalConfig.value.showTooltip || !userData.value) {
      return ''
    }
    
    const user = userData.value
    const content = finalConfig.value.tooltipContent
    
    switch (content) {
      case 'username':
        return user.username
      case 'userinfo':
        return `用户ID: ${user.id}\n用户名: ${user.username}`
      case 'custom':
        return finalConfig.value.customTooltipFormatter(user)
      default:
        return user.username
    }
  })

  const avatarText = computed(() => {
    if (!userData.value) return ''
    
    const user = userData.value
    const fallback = finalConfig.value.avatarFallback
    
    switch (fallback) {
      case 'username':
        return user.username.charAt(0).toUpperCase()
      case 'initials':
        const name = user.username
        if (/[\u4e00-\u9fa5]/.test(name)) {
          return name.charAt(0)
        } else {
          return name.charAt(0).toUpperCase()
        }
      default:
        return user.username.charAt(0).toUpperCase()
    }
  })

  // 事件处理
  const handleClick = (event?: Event) => {
    if (!userData.value || !finalConfig.value.onClick) {
      return
    }
    
    const result = finalConfig.value.onClick(userData.value, props.row)
    
    if (result === false && event) {
      event.preventDefault()
      event.stopPropagation()
    }
  }

  // 样式计算
  const variantClasses = computed(() => {
    const variant = finalConfig.value.variant
    const clickable = finalConfig.value.onClick !== undefined
    
    const baseClasses = []
    
    switch (variant) {
      case 'link':
        baseClasses.push('text-blue-600 hover:text-blue-800 hover:underline')
        break
      case 'badge':
        break
      case 'avatar':
        baseClasses.push('flex items-center')
        break
      default:
        break
    }
    
    if (clickable) {
      baseClasses.push('cursor-pointer')
    }
    
    return baseClasses.join(' ')
  })

  const avatarSizeClasses = computed(() => {
    const size = finalConfig.value.avatarSize
    switch (size) {
      case 'sm':
        return 'w-5 h-5'
      case 'lg':
        return 'w-8 h-8'
      case 'md':
      default:
        return 'w-6 h-6'
    }
  })

  // 工具方法
  const refreshCache = async () => {
    if (!userId.value) return
    
    try {
      await userCache.refreshCache()
      await loadUserData(userId.value)
    } catch (error) {
      console.error('刷新用户缓存失败:', error)
    }
  }

  return {
    isLoading,
    hasError,
    hasData,
    userData,
    finalConfig,
    displayText,
    tooltipText,
    avatarText,
    variantClasses,
    avatarSizeClasses,
    handleClick,
    refreshCache,
    getDisplayText,
  }
}