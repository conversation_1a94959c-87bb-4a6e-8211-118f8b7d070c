/**
 * 智能列缓存系统
 *
 * 主要功能：
 * 1. 列配置智能缓存和复用
 * 2. 基于内容哈希的缓存键
 * 3. 内存使用优化
 * 4. 自动清理和垃圾回收
 */

import { reactive, computed, shallowRef, type Ref } from 'vue'
import type { PluginColumnConfig } from './types'

interface CacheEntry {
  config: any
  hash: string
  usageCount: number
  lastAccessed: number
  memorySize: number
}

interface CacheStats {
  totalEntries: number
  totalMemory: number
  hitRate: number
  missRate: number
  avgUsageCount: number
}

export class SmartColumnCache {
  private cache = new Map<string, CacheEntry>()
  private hashToKey = new Map<string, string>()
  private accessHistory = new Map<string, number[]>()

  // 统计信息
  private stats = reactive({
    hits: 0,
    misses: 0,
    evictions: 0,
    totalRequests: 0,
  })

  // 配置选项
  private config = {
    maxEntries: 200,
    maxMemoryMB: 50,
    ttl: 15 * 60 * 1000, // 15分钟
    cleanupInterval: 3 * 60 * 1000, // 3分钟清理一次
  }

  private cleanupTimer: number | null = null

  constructor(config?: Partial<typeof SmartColumnCache.prototype.config>) {
    if (config) {
      Object.assign(this.config, config)
    }

    this.startCleanupTimer()
  }

  /**
   * 获取或创建列配置
   */
  getOrCreate<T = PluginColumnConfig>(key: string, factory: () => T): T {
    this.stats.totalRequests++

    // 首先检查缓存
    const cached = this.get(key)
    if (cached) {
      this.stats.hits++
      return cached as T
    }

    // 缓存未命中，创建新配置
    this.stats.misses++
    const config = factory()
    this.set(key, config as any)

    return config
  }

  /**
   * 从缓存获取配置
   */
  private get(key: string): any | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    // 检查是否过期
    if (this.isExpired(entry)) {
      this.remove(key)
      return null
    }

    // 更新访问信息
    entry.usageCount++
    entry.lastAccessed = Date.now()
    this.recordAccess(key)

    return entry.config
  }

  /**
   * 设置缓存条目
   */
  private set(key: string, config: any): void {
    const hash = this.generateHash(config)

    // 检查是否已有相同配置（去重）
    const existingKey = this.hashToKey.get(hash)
    if (existingKey && this.cache.has(existingKey)) {
      // 复用现有配置
      const existingEntry = this.cache.get(existingKey)!
      this.cache.set(key, existingEntry)
      this.recordAccess(key)
      return
    }

    const memorySize = this.estimateMemorySize(config)
    const entry: CacheEntry = {
      config,
      hash,
      usageCount: 1,
      lastAccessed: Date.now(),
      memorySize,
    }

    // 检查容量限制
    this.ensureCapacity(memorySize)

    // 添加到缓存
    this.cache.set(key, entry)
    this.hashToKey.set(hash, key)
    this.recordAccess(key)
  }

  /**
   * 移除缓存条目
   */
  private remove(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false

    this.cache.delete(key)
    this.hashToKey.delete(entry.hash)
    this.accessHistory.delete(key)

    return true
  }

  /**
   * 生成配置哈希 - 优化版本，避免 JSON.stringify
   */
  private generateHash(config: any): string {
    // 直接拼接关键字段，避免 JSON.stringify 的开销
    const parts = [
      config.field || '',
      config.title || '',
      config.plugin || '',
      String(config.width || ''),
      this.hashPluginConfig(config.pluginConfig),
    ]

    return this.fastHash(parts.join('|'))
  }

  /**
   * 快速哈希插件配置对象
   */
  private hashPluginConfig(pluginConfig: any): string {
    if (!pluginConfig || typeof pluginConfig !== 'object') {
      return String(pluginConfig || '')
    }

    // 只处理关键配置字段，避免深度遍历
    const keys = ['type', 'variant', 'size', 'color', 'disabled', 'readonly']
    const values = keys.map((key) => String(pluginConfig[key] || '')).join(',')
    return values
  }

  /**
   * 高效的字符串哈希算法 (FNV-1a)
   */
  private fastHash(str: string): string {
    let hash = 2166136261 // FNV offset basis
    for (let i = 0; i < str.length; i++) {
      hash ^= str.charCodeAt(i)
      hash = (hash * 16777619) >>> 0 // FNV prime, 确保32位无符号整数
    }
    return hash.toString(36)
  }

  /**
   * 估算内存大小 - 优化版本，避免 JSON.stringify
   */
  private estimateMemorySize(config: any): number {
    // 基于对象结构估算内存大小（单位：字节）
    let size = 0

    // 基础字段大小估算
    if (config.field) size += config.field.length * 2
    if (config.title) size += config.title.length * 2
    if (config.plugin) size += config.plugin.length * 2
    if (config.width) size += 8 // 数字类型约8字节

    // 插件配置大小估算
    if (config.pluginConfig && typeof config.pluginConfig === 'object') {
      size += Object.keys(config.pluginConfig).length * 20 // 每个属性约20字节
    }

    // 基础对象开销
    size += 100

    return size
  }

  /**
   * 检查是否过期
   */
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.lastAccessed > this.config.ttl
  }

  /**
   * 记录访问历史
   */
  private recordAccess(key: string): void {
    const history = this.accessHistory.get(key) || []
    const now = Date.now()

    // 只保留最近100次访问记录
    history.push(now)
    if (history.length > 100) {
      history.shift()
    }

    this.accessHistory.set(key, history)
  }

  /**
   * 确保缓存容量
   */
  private ensureCapacity(newEntrySize: number): void {
    const currentMemory = this.getTotalMemoryUsage()
    const maxMemoryBytes = this.config.maxMemoryMB * 1024 * 1024

    // 检查内存限制
    if (currentMemory + newEntrySize > maxMemoryBytes) {
      this.evictByMemory(newEntrySize)
    }

    // 检查条目数量限制
    if (this.cache.size >= this.config.maxEntries) {
      this.evictByCount()
    }
  }

  /**
   * 按内存使用进行淘汰
   */
  private evictByMemory(requiredSpace: number): void {
    const entries = Array.from(this.cache.entries())

    // 按优先级排序（使用频率低 + 最后访问时间早的优先淘汰）
    entries.sort(([, a], [, b]) => {
      const scoreA = a.usageCount * 0.7 + (Date.now() - a.lastAccessed) * 0.3
      const scoreB = b.usageCount * 0.7 + (Date.now() - b.lastAccessed) * 0.3
      return scoreA - scoreB
    })

    let freedMemory = 0
    for (const [key] of entries) {
      if (freedMemory >= requiredSpace) break

      const entry = this.cache.get(key)!
      freedMemory += entry.memorySize
      this.remove(key)
      this.stats.evictions++
    }
  }

  /**
   * 按数量进行淘汰
   */
  private evictByCount(): void {
    const entries = Array.from(this.cache.entries())

    // 移除最少使用的条目
    const toRemove = entries
      .sort(([, a], [, b]) => a.usageCount - b.usageCount)
      .slice(0, Math.floor(this.config.maxEntries * 0.1)) // 移除10%

    toRemove.forEach(([key]) => {
      this.remove(key)
      this.stats.evictions++
    })
  }

  /**
   * 获取总内存使用量
   */
  private getTotalMemoryUsage(): number {
    return Array.from(this.cache.values()).reduce(
      (total, entry) => total + entry.memorySize,
      0
    )
  }

  /**
   * 定期清理
   */
  private cleanup(): void {
    const now = Date.now()
    const toRemove: string[] = []

    this.cache.forEach((entry, key) => {
      if (this.isExpired(entry)) {
        toRemove.push(key)
      }
    })

    toRemove.forEach((key) => {
      this.remove(key)
    })

    if (toRemove.length > 0) {
      console.log(`🧹 [SmartColumnCache] 清理了 ${toRemove.length} 个过期条目`)
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    const hitRate =
      this.stats.totalRequests > 0
        ? (this.stats.hits / this.stats.totalRequests) * 100
        : 0

    const avgUsageCount =
      this.cache.size > 0
        ? Array.from(this.cache.values()).reduce(
            (sum, entry) => sum + entry.usageCount,
            0
          ) / this.cache.size
        : 0

    return {
      totalEntries: this.cache.size,
      totalMemory: this.getTotalMemoryUsage(),
      hitRate: Math.round(hitRate * 100) / 100,
      missRate: Math.round((100 - hitRate) * 100) / 100,
      avgUsageCount: Math.round(avgUsageCount * 100) / 100,
    }
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear()
    this.hashToKey.clear()
    this.accessHistory.clear()
    this.stats.hits = 0
    this.stats.misses = 0
    this.stats.evictions = 0
    this.stats.totalRequests = 0
  }

  /**
   * 销毁缓存
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
    this.clear()
  }
}

// 全局智能列缓存实例
export const globalColumnCache = new SmartColumnCache()

// 在开发环境暴露调试接口
if (import.meta.env.DEV && typeof window !== 'undefined') {
  ;(window as any).__COLUMN_CACHE__ = {
    cache: globalColumnCache,
    stats: () => console.log(globalColumnCache.getStats()),
    clear: () => globalColumnCache.clear(),
  }
}
