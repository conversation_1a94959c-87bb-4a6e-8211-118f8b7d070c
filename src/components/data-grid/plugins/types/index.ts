/**
 * 数据网格渲染器类型定义
 */

import type { ButtonVariant, ButtonType } from '../utils/rendererUtils'

/**
 * 基础渲染器属性接口
 */
export interface BaseRendererProps {
  /** 单元格值 */
  value?: any
  /** 行数据 */
  row: any
  /** 字段名 */
  field?: string
  /** 配置ID（用于从配置存储中获取完整配置） */
  configId?: string
  /** 列信息 */
  column?: any
}

/**
 * 基础渲染器配置接口
 */
export interface BaseRendererConfig {
  /** 宽度 */
  width?: number
  /** 其他配置属性 */
  [key: string]: any
}

// 列配置（使用新的类型系统）
export interface PluginColumnConfig extends BaseRendererConfig {
  field: string
  title: string
  width?: number
  plugin?: string
  pluginConfig?: BaseRendererConfig
  slots?: {
    default?: {
      render?: (params: any) => any
      component?: any
      props?: Record<string, any>
    }
  }
  [key: string]: any
}

/**
 * 重新导出工具类型
 */
export type { ButtonVariant, ButtonType }
