# 数据网格渲染器工具函数

这个工具库包含了数据网格渲染器组件的通用工具函数，旨在减少代码重复并提供一致的功能。

## 功能概览

### 样式工具

#### `getTailwindSizeClass(size: number): string`

将像素尺寸转换为 Tailwind CSS 类名。

```typescript
getTailwindSizeClass(32) // 返回 "w-8 h-8"
getTailwindSizeClass(16) // 返回 "w-4 h-4"
```

### HTML 处理工具

#### `containsHtml(str: string): boolean`

检测字符串是否包含 HTML 标签。

```typescript
containsHtml('<span>Hello</span>') // 返回 true
containsHtml('Hello World') // 返回 false
```

#### `sanitizeHtml(html: string, config?: HtmlSanitizeConfig): string`

清理 HTML 字符串，移除不安全的标签和属性。

```typescript
// 使用默认配置
sanitizeHtml('<span class="text-red">Hello</span><script>alert("XSS")</script>')
// 返回 '<span class="text-red">Hello</span>'

// 使用自定义配置
sanitizeHtml('<div>Hello</div>', {
  allowedTags: ['div', 'span'],
  allowedAttributes: ['class'],
})
```

默认允许的标签：`span`, `strong`, `em`, `b`, `i`, `u`, `mark`, `small`, `sub`, `sup`, `br`
默认允许的属性：`class`, `style`

#### `formatContent(value: any, row: any, formatter?: Function, htmlConfig?: HtmlSanitizeConfig)`

格式化内容并自动检测 HTML。

```typescript
const result = formatContent(
  'John Doe',
  { status: 'active' },
  (value, row) => `<strong>${value}</strong> (${row.status})`
)
// 返回: { content: '<strong>John Doe</strong> (active)', isHtml: true }
```

### 按钮工具

#### `getButtonVariant(type?: ButtonType, variant?: ButtonVariant): ButtonVariant`

根据按钮类型获取对应的变体。

```typescript
getButtonVariant('danger') // 返回 'destructive'
getButtonVariant('primary') // 返回 'default'
getButtonVariant(undefined, 'outline') // 返回 'outline' (直接指定的变体优先)
```

#### `getDropdownItemClasses(variant: ButtonVariant): string`

获取下拉菜单项的样式类名。

```typescript
getDropdownItemClasses('destructive')
// 返回 'text-destructive focus:text-destructive focus:bg-destructive/10'
```

#### `mapButtonSize(size?: string)`

映射按钮尺寸到标准尺寸。

```typescript
mapButtonSize('small') // 返回 'sm'
mapButtonSize('medium') // 返回 'default'
mapButtonSize('large') // 返回 'lg'
```

## 类型定义

```typescript
export type ButtonVariant =
  | 'link'
  | 'default'
  | 'destructive'
  | 'outline'
  | 'secondary'
  | 'ghost'

export type ButtonType = 'primary' | 'success' | 'warning' | 'danger' | 'info'

export interface HtmlSanitizeConfig {
  allowedTags?: string[]
  allowedAttributes?: string[]
}
```

## 使用示例

### 在渲染器组件中使用

```typescript
import {
  getButtonVariant,
  getDropdownItemClasses,
  formatContent,
  getTailwindSizeClass,
} from '@/components/data-grid/plugins/utils'

// 在组件中使用
const buttonVariant = getButtonVariant(action.type, action.variant)
const dropdownClasses = getDropdownItemClasses(buttonVariant)
const iconSizeClass = getTailwindSizeClass(24) // "w-6 h-6"
const formatted = formatContent(value, row, formatter)
```

### 扩展 HTML 清理配置

```typescript
import {
  sanitizeHtml,
  DEFAULT_HTML_SANITIZE_CONFIG,
} from '@/components/data-grid/plugins/utils'

const customConfig = {
  ...DEFAULT_HTML_SANITIZE_CONFIG,
  allowedTags: [...DEFAULT_HTML_SANITIZE_CONFIG.allowedTags, 'div', 'p'],
  allowedAttributes: [
    ...DEFAULT_HTML_SANITIZE_CONFIG.allowedAttributes,
    'id',
    'data-*',
  ],
}

const cleanHtml = sanitizeHtml(dirtyHtml, customConfig)
```

## 安全性

- HTML 清理功能默认只允许安全的标签和属性
- 自动移除潜在的 XSS 攻击向量
- 可以通过配置自定义允许的标签和属性列表

## 扩展性

这个工具库设计为可扩展的。如果需要添加新的通用功能：

1. 在 `rendererUtils.ts` 中添加新函数
2. 在 `index.ts` 中导出
3. 更新此 README 文档
4. 在相关渲染器组件中使用

## 迁移指南

从旧的本地函数迁移到工具函数：

```typescript
// 旧方式
const getButtonVariant = (action) => {
  switch (action.type) {
    case 'danger':
      return 'destructive'
    // ...
  }
}

// 新方式
import { getButtonVariant } from '@/components/data-grid/plugins/utils'
const variant = getButtonVariant(action.type, action.variant)
```
