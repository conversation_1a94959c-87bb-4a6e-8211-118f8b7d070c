/**
 * 数据网格渲染器通用工具函数
 */

import { cn } from '@/lib/utils'

/**
 * 获取 Tailwind CSS 尺寸类名
 * @param size 像素尺寸
 * @returns Tailwind CSS 类名，如 'w-8 h-8'
 */
export const getTailwindSizeClass = (size: number): string => {
  const units = Math.floor(size / 4)
  return `w-${units} h-${units}`
}

/**
 * 检测字符串是否包含 HTML 标签
 * @param str 待检测的字符串
 * @returns 是否包含 HTML 标签
 */
export const containsHtml = (str: string): boolean => {
  if (!str || typeof str !== 'string') return false

  // 检测是否包含 HTML 标签的正则表达式
  const htmlRegex = /<[^>]*>/
  return htmlRegex.test(str.trim())
}

/**
 * HTML 清理配置
 */
export interface HtmlSanitizeConfig {
  allowedTags?: string[]
  allowedAttributes?: string[]
}

/**
 * 默认的 HTML 清理配置
 */
export const DEFAULT_HTML_SANITIZE_CONFIG: HtmlSanitizeConfig = {
  allowedTags: [
    'span',
    'strong',
    'em',
    'b',
    'i',
    'u',
    'mark',
    'small',
    'sub',
    'sup',
    'br',
  ],
  allowedAttributes: ['class', 'style'],
}

/**
 * 简单的 HTML 清理函数（用于提高安全性）
 * @param html 待清理的 HTML 字符串
 * @param config 清理配置，可选
 * @returns 清理后的 HTML 字符串
 */
export const sanitizeHtml = (
  html: string,
  config: HtmlSanitizeConfig = DEFAULT_HTML_SANITIZE_CONFIG
): string => {
  const { allowedTags = [], allowedAttributes = [] } = config

  // 简单的标签清理，移除不在允许列表中的标签
  return html.replace(
    /<(\/?)([\w-]+)([^>]*)>/gi,
    (match, slash, tag, attrs) => {
      if (!allowedTags.includes(tag.toLowerCase())) {
        return '' // 移除不允许的标签
      }

      // 清理属性，只保留允许的属性
      const cleanAttrs = attrs.replace(
        /(\w+)=/gi,
        (attrMatch: string, attrName: string) => {
          return allowedAttributes.includes(attrName.toLowerCase())
            ? attrMatch
            : ''
        }
      )

      return `<${slash}${tag}${cleanAttrs}>`
    }
  )
}

/**
 * 按钮变体类型
 */
export type ButtonVariant =
  | 'link'
  | 'default'
  | 'destructive'
  | 'outline'
  | 'secondary'
  | 'ghost'

/**
 * 按钮类型
 */
/**
 * 统一的样式主题配置
 * 遵循DRY原则，避免在多个渲染器中重复定义样式
 */
interface ThemeColorConfig {
  bg: string
  text: string
  dot: string
  border: string
  hover?: string
}

/**
 * 统一的主题颜色映射
 * 所有渲染器都使用这套统一的颜色配置
 */
export const THEME_COLORS: Record<string, ThemeColorConfig> = {
  primary: {
    bg: 'bg-blue-100',
    text: 'text-blue-800',
    dot: 'bg-blue-500',
    border: 'border-blue-200',
    hover: 'hover:bg-blue-200',
  },
  success: {
    bg: 'bg-green-100',
    text: 'text-green-800',
    dot: 'bg-green-500',
    border: 'border-green-200',
    hover: 'hover:bg-green-200',
  },
  warning: {
    bg: 'bg-yellow-100',
    text: 'text-yellow-800',
    dot: 'bg-yellow-500',
    border: 'border-yellow-200',
    hover: 'hover:bg-yellow-200',
  },
  danger: {
    bg: 'bg-red-100',
    text: 'text-red-800',
    dot: 'bg-red-500',
    border: 'border-red-200',
    hover: 'hover:bg-red-200',
  },
  info: {
    bg: 'bg-gray-100',
    text: 'text-gray-800',
    dot: 'bg-gray-500',
    border: 'border-gray-200',
    hover: 'hover:bg-gray-200',
  },
  default: {
    bg: 'bg-muted',
    text: 'text-muted-foreground',
    dot: 'bg-gray-400',
    border: 'border-gray-200',
    hover: 'hover:bg-muted/80',
  },
} as const

/**
 * 获取主题颜色配置
 * 统一的颜色获取接口，支持类型安全
 */
export function getThemeColors(type: string): ThemeColorConfig {
  return THEME_COLORS[type] || THEME_COLORS.default
}

/**
 * 创建统一的样式类名生成器
 * 遵循KISS原则，简化样式类名的生成逻辑
 */
export function createThemeClassBuilder(type: string) {
  const colors = getThemeColors(type)

  return {
    badge: (extraClasses = '') =>
      cn(
        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
        colors.bg,
        colors.text,
        extraClasses
      ),
    dot: (extraClasses = '') =>
      cn('w-2 h-2 rounded-full', colors.dot, extraClasses),
    dotContainer: (extraClasses = '') =>
      cn('inline-flex items-center space-x-2', extraClasses),
    text: (extraClasses = '') =>
      cn('text-sm font-medium', colors.text, extraClasses),
    button: (extraClasses = '') =>
      cn(
        'inline-flex items-center justify-center',
        colors.bg,
        colors.text,
        colors.hover,
        extraClasses
      ),
  }
}
export type ButtonType = 'primary' | 'success' | 'warning' | 'danger' | 'info'

/**
 * 根据按钮类型获取对应的变体
 * @param type 按钮类型
 * @param variant 直接指定的变体（优先级更高）
 * @returns 按钮变体
 */
export const getButtonVariant = (
  type?: ButtonType,
  variant?: ButtonVariant
): ButtonVariant => {
  if (variant) return variant

  // 根据类型映射变体
  switch (type) {
    case 'primary':
      return 'default'
    case 'success':
      return 'default'
    case 'warning':
      return 'outline'
    case 'danger':
      return 'destructive'
    case 'info':
      return 'secondary'
    default:
      return 'outline'
  }
}

/**
 * 获取下拉菜单项的样式类
 * @param variant 按钮变体
 * @returns CSS 类名字符串
 */
export const getDropdownItemClasses = (variant: ButtonVariant): string => {
  switch (variant) {
    case 'destructive':
      return 'text-destructive focus:text-destructive focus:bg-destructive/10'
    case 'default':
      return 'text-primary focus:text-primary focus:bg-primary/10'
    case 'secondary':
      return 'text-secondary-foreground focus:text-secondary-foreground focus:bg-secondary/10'
    case 'outline':
      return 'text-muted-foreground focus:text-foreground focus:bg-muted/10'
    case 'ghost':
      return 'text-muted-foreground focus:text-foreground focus:bg-muted/10'
    case 'link':
      return 'text-primary focus:text-primary focus:bg-primary/10'
    default:
      return 'text-muted-foreground focus:text-foreground focus:bg-muted/10'
  }
}

/**
 * 按钮尺寸映射
 */
export const mapButtonSize = (
  size?: string
): 'default' | 'sm' | 'lg' | 'icon' | 'xs' => {
  if (!size) return 'sm'

  // 映射到 Button 组件支持的大小
  switch (size) {
    case 'small':
      return 'sm'
    case 'medium':
      return 'default'
    case 'large':
      return 'lg'
    default:
      return size as 'default' | 'sm' | 'lg' | 'icon' | 'xs'
  }
}

/**
 * 格式化内容（自动检测 HTML 并处理）
 * @param value 原始值
 * @param row 行数据
 * @param formatter 格式化函数
 * @param htmlConfig HTML 清理配置
 * @returns 格式化后的内容对象
 */
export const formatContent = (
  value: any,
  row: any,
  formatter?: (value: any, row: any) => string,
  htmlConfig?: HtmlSanitizeConfig
): { content: string; isHtml: boolean } => {
  if (!formatter) {
    return { content: String(value || ''), isHtml: false }
  }

  try {
    const formattedValue = formatter(value, row)
    const isHtml = containsHtml(formattedValue)

    return {
      content: isHtml
        ? sanitizeHtml(formattedValue, htmlConfig)
        : formattedValue,
      isHtml,
    }
  } catch (error) {
    console.warn('Formatter error:', error)
    return { content: String(value || ''), isHtml: false }
  }
}
