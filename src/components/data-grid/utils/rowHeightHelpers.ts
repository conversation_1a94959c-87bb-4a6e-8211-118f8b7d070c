/**
 * DataGrid 行高自动调整工具函数
 *
 * 用于在渲染器组件中自动调整表格行高以适应内容
 */

import { nextTick } from 'vue'

/**
 * 安全的 nextTick 操作，检查元素是否仍在 DOM 中
 */
const safeNextTick = (element: HTMLElement | null, callback: () => void) => {
  if (!element) return
  
  nextTick(() => {
    // 检查元素是否仍在 DOM 中且未被销毁
    if (element.isConnected && document.contains(element)) {
      try {
        callback()
      } catch (error) {
        console.warn('Safe nextTick callback error:', error)
      }
    }
  })
}

/**
 * 自动调整表格行高以适应内容
 *
 * @param containerElement - 渲染器容器元素
 * @param options - 调整选项
 */
export interface AutoRowHeightOptions {
  /** 最小行高，默认40px */
  minHeight?: number
  /** 是否强制调整，默认false */
  force?: boolean
  /** 额外的内边距，默认8px */
  padding?: number
  /** 是否添加标识类，默认true */
  addMarkerClass?: boolean
}

// 批量 DOM 操作缓存，减少重排重绘
const pendingRowAdjustments = new Map<HTMLElement, {
  requiredHeight: number
  options: AutoRowHeightOptions
}>()

let adjustmentScheduled = false

/**
 * 批量执行所有待调整的行高操作
 */
function flushRowAdjustments() {
  if (pendingRowAdjustments.size === 0) return

  // 一次性读取所有需要的尺寸信息，避免多次重排
  const adjustments = Array.from(pendingRowAdjustments.entries()).map(([rowElement, { requiredHeight, options }]) => {
    const currentHeight = rowElement.offsetHeight
    return { rowElement, requiredHeight, currentHeight, options }
  })

  // 批量应用样式变更，最小化重排
  adjustments.forEach(({ rowElement, requiredHeight, currentHeight, options }) => {
    const { force = false, addMarkerClass = true } = options

    if (force || requiredHeight > currentHeight) {
      // 添加标识类
      if (addMarkerClass) {
        rowElement.classList.add('has-auto-height-renderer')
      }

      // 批量设置行样式
      rowElement.style.cssText += `height: auto; min-height: ${requiredHeight}px;`

      // 批量设置单元格样式
      const cells = rowElement.querySelectorAll('.vxe-body--column')
      const cellStyleText = `height: auto; min-height: ${requiredHeight}px;`
      
      cells.forEach((cell: Element) => {
        if (cell instanceof HTMLElement) {
          cell.style.cssText += cellStyleText

          // 处理 .vxe-cell 元素
          const vxeCell = cell.querySelector('.vxe-cell') as HTMLElement
          if (vxeCell) {
            vxeCell.style.cssText += cellStyleText
          }
        }
      })

      // 同步固定列的行高
      syncFixedColumnHeight(rowElement, requiredHeight)
    }
  })

  // 清理缓存
  pendingRowAdjustments.clear()
  adjustmentScheduled = false
}

export function adjustRowHeight(
  containerElement: HTMLElement | null,
  options: AutoRowHeightOptions = {}
): void {
  if (!containerElement) return

  const {
    minHeight = 40,
    padding = 8,
  } = options

  // 查找最近的表格行元素
  let rowElement = containerElement.closest('.vxe-body--row') as HTMLElement
  if (!rowElement) {
    rowElement = containerElement.closest('tr') as HTMLElement
  }

  if (!rowElement) return

  // 获取容器的实际高度
  const containerHeight = containerElement.scrollHeight
  const requiredHeight = Math.max(containerHeight + padding, minHeight)

  // 将调整操作加入批量处理队列
  pendingRowAdjustments.set(rowElement, { requiredHeight, options })

  // 如果尚未安排批量处理，则在下一个微任务中执行
  if (!adjustmentScheduled) {
    adjustmentScheduled = true
    safeNextTick(containerElement, flushRowAdjustments)
  }
}

/**
 * 同步固定列的行高
 *
 * @param sourceRow - 源行元素
 * @param height - 目标高度
 */
function syncFixedColumnHeight(sourceRow: HTMLElement, height: number): void {
  // 获取行索引
  const rowIndex = Array.from(sourceRow.parentElement?.children || []).indexOf(
    sourceRow
  )
  if (rowIndex === -1) return

  // 查找表格容器
  const tableWrapper = sourceRow.closest('.vxe-table')
  if (!tableWrapper) return

  // 查找所有固定列容器
  const allFixedContainers = tableWrapper.querySelectorAll(
    '[class*="fixed"], [class*="Fixed"]'
  )

  // 遍历所有固定列容器
  allFixedContainers.forEach((container) => {
    // 在每个容器中查找对应行索引的行
    const rows = container.querySelectorAll('.vxe-body--row, tr[data-rowid]')
    const targetRow = rows[rowIndex] as HTMLElement

    if (targetRow) {
      targetRow.style.height = 'auto'
      targetRow.style.minHeight = `${height}px`
      targetRow.classList.add('has-auto-height-renderer')

      // 同步该行内的所有单元格
      const cells = targetRow.querySelectorAll('.vxe-body--column, td')
      cells.forEach((cell: Element) => {
        if (cell instanceof HTMLElement) {
          cell.style.height = 'auto'
          cell.style.minHeight = `${height}px`

          // 同时调整单元格内部的 .vxe-cell 元素
          const vxeCell = cell.querySelector('.vxe-cell')
          if (vxeCell instanceof HTMLElement) {
            vxeCell.style.height = 'auto'
            vxeCell.style.minHeight = `${height}px`
          }
        }
      })
    }
  })
}

/**
 * 检查是否需要自动调整行高
 *
 * @param config - 渲染器配置
 * @returns 是否需要调整
 */
export function shouldAdjustRowHeight(config: any): boolean {
  // 检查是否有可能导致多行内容的配置
  const hasVerticalLayout = config?.subs?.layout === 'vertical'
  const hasMultipleSubItems = (config?.subs?.items?.length || 0) > 1
  const hasIcon = !!config?.icon
  const hasActions = (config?.actions?.length || 0) > 0
  const hasLongContent = config?.main?.multiline || config?.allowWrap

  return (
    hasVerticalLayout ||
    (hasMultipleSubItems && hasIcon) ||
    hasActions ||
    hasLongContent
  )
}

/**
 * 为渲染器组件创建自动行高调整的组合式函数
 *
 * @param containerRef - 容器元素的ref
 * @param configRef - 配置的ref
 * @param options - 调整选项
 */
export function useAutoRowHeight(
  containerRef: { value: HTMLElement | null },
  configRef: { value: any },
  options: AutoRowHeightOptions = {}
) {
  const adjustHeight = () => {
    if (shouldAdjustRowHeight(configRef.value)) {
      adjustRowHeight(containerRef.value, options)
    }
  }

  return {
    adjustHeight,
    shouldAdjust: () => shouldAdjustRowHeight(configRef.value),
  }
}

/**
 * 重置行高为默认值
 *
 * @param containerElement - 渲染器容器元素
 */
export function resetRowHeight(containerElement: HTMLElement | null): void {
  if (!containerElement) return

  safeNextTick(containerElement, () => {
    // 查找最近的表格行元素
    let rowElement = containerElement.closest('.vxe-body--row') as HTMLElement
    if (!rowElement) {
      rowElement = containerElement.closest('tr') as HTMLElement
    }

    if (!rowElement) return

    // 移除标识类
    rowElement.classList.remove('has-auto-height-renderer')

    // 重置行高样式
    rowElement.style.height = ''
    rowElement.style.minHeight = ''

    // 重置所有单元格的高度样式
    const cells = rowElement.querySelectorAll('.vxe-body--column')
    cells.forEach((cell: Element) => {
      if (cell instanceof HTMLElement) {
        cell.style.height = ''
        cell.style.minHeight = ''

        // 同时重置单元格内部的 .vxe-cell 元素
        const vxeCell = cell.querySelector('.vxe-cell')
        if (vxeCell instanceof HTMLElement) {
          vxeCell.style.height = ''
          vxeCell.style.minHeight = ''
        }
      }
    })

    // 重置固定列的行高
    resetFixedColumnHeight(rowElement)
  })
}

/**
 * 重置固定列的行高
 *
 * @param sourceRow - 源行元素
 */
function resetFixedColumnHeight(sourceRow: HTMLElement): void {
  // 获取行索引
  const rowIndex = Array.from(sourceRow.parentElement?.children || []).indexOf(
    sourceRow
  )
  if (rowIndex === -1) return

  // 查找表格容器
  const tableWrapper = sourceRow.closest('.vxe-table')
  if (!tableWrapper) return

  // 查找所有固定列容器
  const allFixedContainers = tableWrapper.querySelectorAll(
    '[class*="fixed"], [class*="Fixed"]'
  )

  // 遍历所有固定列容器
  allFixedContainers.forEach((container) => {
    // 在每个容器中查找对应行索引的行
    const rows = container.querySelectorAll('.vxe-body--row, tr[data-rowid]')
    const targetRow = rows[rowIndex] as HTMLElement

    if (targetRow) {
      targetRow.style.height = ''
      targetRow.style.minHeight = ''
      targetRow.classList.remove('has-auto-height-renderer')

      // 重置该行内的所有单元格
      const cells = targetRow.querySelectorAll('.vxe-body--column, td')
      cells.forEach((cell: Element) => {
        if (cell instanceof HTMLElement) {
          cell.style.height = ''
          cell.style.minHeight = ''

          // 同时重置单元格内部的 .vxe-cell 元素
          const vxeCell = cell.querySelector('.vxe-cell')
          if (vxeCell instanceof HTMLElement) {
            vxeCell.style.height = ''
            vxeCell.style.minHeight = ''
          }
        }
      })
    }
  })
}
