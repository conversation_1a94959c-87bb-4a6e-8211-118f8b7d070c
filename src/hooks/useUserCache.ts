import { ref, computed, watch, type ComputedRef } from 'vue'
import userCache, { type AllUsersName } from '@/utils/userCache'

/**
 * 用户缓存组合式函数
 * 提供在Vue组件中使用用户缓存的功能
 */
export function useUserCache() {
  const loading = ref(false)
  const error = ref<string | null>(null)

  /**
   * 根据用户ID获取用户名（响应式）
   */
  const getUserName = async (userId: number | string) => {
    if (!userId) return null

    loading.value = true
    error.value = null

    try {
      const username = await userCache.getUserName(userId)
      return username
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取用户名失败'
      console.error('获取用户名失败:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  /**
   * 根据用户ID获取用户信息（响应式）
   */
  const getUserInfo = async (userId: number | string) => {
    if (!userId) return null

    loading.value = true
    error.value = null

    try {
      const userInfo = await userCache.getUserInfo(userId)
      return userInfo
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取用户信息失败'
      console.error('获取用户信息失败:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取所有用户信息（响应式）
   */
  const getAllUsers = async (forceRefresh: boolean = false) => {
    loading.value = true
    error.value = null

    try {
      const users = await userCache.getAllUsers(forceRefresh)
      return users
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取用户列表失败'
      console.error('获取用户列表失败:', err)
      return []
    } finally {
      loading.value = false
    }
  }

  /**
   * 根据用户名查找用户ID（响应式）
   */
  const getUserIdByUsername = async (username: string) => {
    if (!username) return null

    loading.value = true
    error.value = null

    try {
      const userId = await userCache.getUserIdByUsername(username)
      return userId
    } catch (err) {
      error.value = err instanceof Error ? err.message : '查找用户ID失败'
      console.error('查找用户ID失败:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  /**
   * 搜索用户（响应式）
   */
  const searchUsers = async (keyword: string) => {
    if (!keyword) return []

    loading.value = true
    error.value = null

    try {
      const users = await userCache.searchUsers(keyword)
      return users
    } catch (err) {
      error.value = err instanceof Error ? err.message : '搜索用户失败'
      console.error('搜索用户失败:', err)
      return []
    } finally {
      loading.value = false
    }
  }

  /**
   * 刷新缓存（响应式）
   */
  const refreshCache = async () => {
    loading.value = true
    error.value = null

    try {
      await userCache.refreshCache()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '刷新缓存失败'
      console.error('刷新缓存失败:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 清除缓存（响应式）
   */
  const clearCache = () => {
    userCache.clearCache()
  }

  /**
   * 获取缓存统计信息（响应式）
   */
  const getCacheStats = () => {
    return userCache.getCacheStats()
  }

  return {
    // 状态
    loading: computed(() => loading.value),
    error: computed(() => error.value),

    // 方法
    getUserName,
    getUserInfo,
    getAllUsers,
    getUserIdByUsername,
    searchUsers,
    refreshCache,
    clearCache,
    getCacheStats,
  }
}

/**
 * 用户ID转名称的响应式工具函数
 * 用于在模板中直接显示用户名
 */
export function useUserName(
  userId: number | string | ComputedRef<number | string>
) {
  const username = ref<string | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  const loadUserName = async () => {
    const currentUserId =
      typeof userId === 'object' && 'value' in userId ? userId.value : userId

    if (!currentUserId) {
      username.value = null
      return
    }

    loading.value = true
    error.value = null

    try {
      const result = await userCache.getUserName(currentUserId)
      username.value = result
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取用户名失败'
      console.error('获取用户名失败:', err)
      username.value = null
    } finally {
      loading.value = false
    }
  }

  // 初始加载
  loadUserName()

  // 如果是响应式，监听变化
  if (typeof userId === 'object' && 'value' in userId) {
    watch(userId, loadUserName)
  }

  return {
    username: computed(() => username.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    reload: loadUserName,
  }
}

/**
 * 批量用户ID转名称的工具函数
 */
export function useBatchUserNames(
  userIds: (number | string)[] | ComputedRef<(number | string)[]>
) {
  const userNames = ref<Map<number | string, string>>(new Map())
  const loading = ref(false)
  const error = ref<string | null>(null)

  const loadUserNames = async () => {
    const currentUserIds =
      typeof userIds === 'object' && 'value' in userIds
        ? userIds.value
        : userIds

    if (!currentUserIds.length) {
      userNames.value.clear()
      return
    }

    loading.value = true
    error.value = null

    try {
      const newUserNames = new Map<number | string, string>()

      for (const userId of currentUserIds) {
        const username = await userCache.getUserName(userId)
        if (username) {
          newUserNames.set(userId, username)
        }
      }

      userNames.value = newUserNames
    } catch (err) {
      error.value = err instanceof Error ? err.message : '批量获取用户名失败'
      console.error('批量获取用户名失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 初始加载
  loadUserNames()

  // 如果是响应式，监听变化
  if (typeof userIds === 'object' && 'value' in userIds) {
    watch(userIds, loadUserNames)
  }

  return {
    userNames: computed(() => userNames.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    reload: loadUserNames,
  }
}
