<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold mb-2">审计信息渲染器示例</h1>
      <p class="text-muted-foreground">演示审计信息复合列的不同渲染格式和配置选项</p>
    </div>

    <!-- 测试数据表格 -->
    <div class="space-y-8">
      <!-- 基础审计信息列 -->
      <div class="border rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-4">基础审计信息</h3>
        <DataGrid
          v-bind="basicGridProps"
          class="border-0"
        />
      </div>

      <!-- 紧凑审计信息列 -->
      <div class="border rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-4">紧凑审计信息</h3>
        <DataGrid
          v-bind="compactGridProps"
          class="border-0"
        />
      </div>

      <!-- 自定义配置审计信息列 -->
      <div class="border rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-4">自定义配置</h3>
        <DataGrid
          v-bind="customGridProps"
          class="border-0"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import DataGrid from '@/components/data-grid/core/DataGrid.vue'
import { getPluginManager } from '@/components/data-grid/plugins'

// 模拟测试数据
const testData = [
  {
    id: 1,
    name: '用户管理',
    created_by: 1001,
    updated_by: 1002,
    created_at: '2025-01-05T08:30:00Z',
    updated_at: '2025-08-10T14:20:30Z'
  },
  {
    id: 2,
    name: '角色配置',
    created_by: 1003,
    updated_by: null,
    created_at: '2025-08-10T09:15:22Z',
    updated_at: null
  },
  {
    id: 3,
    name: '权限设置',
    created_by: 1004,
    updated_by: 1005,
    created_at: '2024-12-20T16:45:10Z',
    updated_at: '2025-08-09T11:30:45Z'
  },
  {
    id: 4,
    name: '系统配置',
    created_by: null,
    updated_by: 1006,
    created_at: '2025-08-08T13:20:00Z',
    updated_at: '2025-08-10T10:10:15Z'
  }
]

// 获取插件管理器
const pluginManager = getPluginManager()
const helper = pluginManager.getColumnHelper()

// 基础审计信息表格配置
const basicGridProps = computed(() => {
  const columns = [
    {
      field: 'id',
      title: 'ID',
      width: 80,
      align: 'center'
    },
    {
      field: 'name',
      title: '名称',
      width: 120
    },
    helper.auditInfo('审计信息')
  ]

  return {
    gridOptions: {
      columns,
      data: testData,
      height: 300,
      stripe: true,
      border: true
    }
  }
})

// 紧凑审计信息表格配置
const compactGridProps = computed(() => {
  const columns = [
    {
      field: 'id',
      title: 'ID',
      width: 80,
      align: 'center'
    },
    {
      field: 'name',
      title: '名称',
      width: 120
    },
    helper.auditInfoCompact('操作信息')
  ]

  return {
    gridOptions: {
      columns,
      data: testData,
      height: 300,
      stripe: true,
      border: true
    }
  }
})

// 自定义配置审计信息表格
const customGridProps = computed(() => {
  const columns = [
    {
      field: 'id',
      title: 'ID',
      width: 80,
      align: 'center'
    },
    {
      field: 'name',
      title: '名称',
      width: 120
    },
    helper.auditInfo('详细操作记录', {
      format: 'detailed',
      dateFormat: {
        format: 'full',
        showSeconds: true
      },
      userDisplay: {
        format: 'username',
        enableUserQuery: true,
        unknownUserText: '系统'
      },
      theme: {
        fontSize: 'sm',
        spacing: 'comfortable',
        textColor: 'secondary'
      }
    })
  ]

  return {
    gridOptions: {
      columns,
      data: testData,
      height: 300,
      stripe: true,
      border: true
    }
  }
})
</script>